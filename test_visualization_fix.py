#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化修复
"""

import os
import sys
import numpy as np

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.utils import DataProcessor
    from src.clean_visualization import CleanFluidVisualization
except ImportError:
    from utils import DataProcessor
    from clean_visualization import CleanFluidVisualization

def test_visualization_fix():
    """测试可视化修复"""
    print("🧪 测试可视化维度修复...")
    
    # 1. 加载地图数据
    print("📍 加载海冰地图数据...")
    map_data = DataProcessor.load_sea_ice_map('sea_ice_419.xlsx')
    if map_data is None:
        print("❌ 无法加载地图数据")
        return False
    
    print(f"✅ 地图加载成功")
    print(f"   原始地图: {map_data['original_shape']}")
    print(f"   扩展地图: {map_data['extended_map'].shape}")
    
    # 2. 创建测试数据
    grid_map = map_data['extended_map']
    h, w = grid_map.shape
    
    # 创建测试速度场（与grid_map相同维度）
    test_speed = np.random.random((h, w)) * 0.1
    test_u = np.random.random((h, w)) * 0.05 - 0.025
    test_v = np.random.random((h, w)) * 0.05 - 0.025
    test_vorticity = np.random.random((h, w)) * 0.01 - 0.005
    test_rho = np.ones((h, w)) + np.random.random((h, w)) * 0.1
    
    print(f"📊 测试数据创建完成")
    print(f"   grid_map形状: {grid_map.shape}")
    print(f"   速度场形状: {test_speed.shape}")
    print(f"   维度匹配: {grid_map.shape == test_speed.shape}")
    
    # 3. 测试可视化函数
    visualizer = CleanFluidVisualization()
    start_point = map_data['start_point']
    end_point = map_data['end_point']
    
    os.makedirs('test_result', exist_ok=True)
    
    try:
        # 测试速度场可视化
        print("🎨 测试速度场可视化...")
        file_path = visualizer.create_speed_field_plot(
            grid_map, test_speed, start_point, end_point,
            'test_result/test_velocity_field.png'
        )
        print(f"✅ 速度场可视化成功: {file_path}")
        
        # 测试速度X分量可视化
        print("🎨 测试速度X分量可视化...")
        file_path = visualizer.create_velocity_x_plot(
            grid_map, test_u, start_point, end_point,
            'test_result/test_velocity_x.png'
        )
        print(f"✅ 速度X分量可视化成功: {file_path}")
        
        # 测试速度Y分量可视化
        print("🎨 测试速度Y分量可视化...")
        file_path = visualizer.create_velocity_y_plot(
            grid_map, test_v, start_point, end_point,
            'test_result/test_velocity_y.png'
        )
        print(f"✅ 速度Y分量可视化成功: {file_path}")
        
        # 测试涡量场可视化
        print("🎨 测试涡量场可视化...")
        file_path = visualizer.create_vorticity_plot(
            grid_map, test_vorticity, start_point, end_point,
            'test_result/test_vorticity.png'
        )
        print(f"✅ 涡量场可视化成功: {file_path}")
        
        # 测试压力场可视化
        print("🎨 测试压力场可视化...")
        file_path = visualizer.create_pressure_plot(
            grid_map, test_rho, start_point, end_point,
            'test_result/test_pressure.png'
        )
        print(f"✅ 压力场可视化成功: {file_path}")
        
        print("\n🎉 所有可视化测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 可视化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dimension_mismatch():
    """测试维度不匹配的情况"""
    print("\n🧪 测试维度不匹配处理...")
    
    # 创建不同维度的测试数据
    grid_map = np.zeros((256, 256))  # 原始地图大小
    test_speed = np.random.random((308, 308)) * 0.1  # 扩展地图大小
    
    print(f"📊 维度不匹配测试数据")
    print(f"   grid_map形状: {grid_map.shape}")
    print(f"   速度场形状: {test_speed.shape}")
    print(f"   维度匹配: {grid_map.shape == test_speed.shape}")
    
    # 测试可视化函数是否能处理维度不匹配
    visualizer = CleanFluidVisualization()
    start_point = (25, 25)
    end_point = (282, 282)
    
    try:
        file_path = visualizer.create_speed_field_plot(
            grid_map, test_speed, start_point, end_point,
            'test_result/test_dimension_mismatch.png'
        )
        print(f"✅ 维度不匹配处理成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 维度不匹配处理失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始可视化修复测试")
    print("=" * 60)
    
    # 测试正常情况
    success1 = test_visualization_fix()
    
    # 测试维度不匹配情况
    success2 = test_dimension_mismatch()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过！可视化修复成功。")
    else:
        print("❌ 部分测试失败，需要进一步修复。")
