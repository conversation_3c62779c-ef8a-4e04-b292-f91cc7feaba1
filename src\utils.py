"""
工具函数模块
包含数据处理、网格操作、性能分析等实用函数
"""

import numpy as np
import pandas as pd
import time
from typing import Tuple, List, Optional, Dict, Any
from scipy import ndimage
import os
import matplotlib.pyplot as plt

class DataProcessor:
    """数据处理工具类"""

    @staticmethod
    def load_excel_data(file_path: str) -> Optional[np.ndarray]:
        """
        从Excel文件加载数据

        参数:
        file_path: Excel文件路径

        返回:
        二进制网格地图或None
        """
        try:
            data = pd.read_excel(file_path, header=None, usecols=range(256), nrows=256)
            numpy_array = data.to_numpy()

            # 转换为二进制地图：0表示流体，1表示障碍物
            binary_map = np.where(numpy_array == 0, 255, np.where(numpy_array == 255, 0, numpy_array))
            binary_map = binary_map / 255
            binary_map = binary_map.astype(int)

            return binary_map
        except Exception as e:
            print(f"加载Excel数据时出错: {e}")
            return None

    @staticmethod
    def load_sea_ice_map(file_path: str = "sea_ice_419.xlsx") -> Optional[Dict[str, np.ndarray]]:
        """
        加载sea_ice_419海冰地图数据

        参数:
        file_path: 海冰地图文件路径

        返回:
        包含原始地图和扩展地图的字典
        """
        try:
            print(f"🗺️ 正在加载海冰地图: {file_path}")

            # 读取Excel文件
            data = pd.read_excel(file_path, header=None)
            numpy_array = data.to_numpy()

            print(f"  原始数据形状: {numpy_array.shape}")
            print(f"  数据范围: {np.min(numpy_array)} - {np.max(numpy_array)}")

            # 处理NaN值
            numpy_array = np.nan_to_num(numpy_array, nan=0)

            # 根据要求：大于0为障碍物，等于0为流体
            original_map = (numpy_array > 0).astype(int)

            # 统计信息
            total_cells = original_map.size
            obstacle_cells = np.sum(original_map == 1)
            fluid_cells = np.sum(original_map == 0)

            print(f"  原始地图形状: {original_map.shape}")
            print(f"  障碍物单元格: {obstacle_cells} ({obstacle_cells/total_cells*100:.1f}%)")
            print(f"  流体单元格: {fluid_cells} ({fluid_cells/total_cells*100:.1f}%)")

            # 检查地图是否有效（需要有流体区域）
            if fluid_cells == 0:
                print("  ⚠️ 警告: 地图中没有流体区域！")
                return None

            if obstacle_cells == 0:
                print("  ⚠️ 警告: 地图中没有障碍物！")

            # 自动检测标签格式并提供交换选项
            # 应用标签交换
            original_map = np.where(numpy_array == 0, 1, np.where(numpy_array == 255, 0, numpy_array))
            original_map=original_map

            plt.figure(figsize=(10, 10), dpi=500)
            plt.imshow(original_map, cmap='gray_r', origin='upper')
            plt.title("original_map", fontsize=14, fontname='Times New Roman')
            plt.axis('off')
            plt.show()

            # 创建边界扩展地图用于LBM计算
            h, w = original_map.shape
            extension_size = 10  # 边界扩展大小

            # 创建扩展地图
            extended_h = h + 2 * extension_size
            extended_w = w + 2 * extension_size
            extended_map = np.ones((extended_h, extended_w))  # 边界为障碍物

            # 将原始地图复制到扩展地图中心
            extended_map[extension_size:extension_size+h, extension_size:extension_size+w] = original_map

            plt.figure(figsize=(10, 10), dpi=500)
            plt.imshow(extended_map, cmap='gray_r', origin='upper')
            plt.title("extended_map1", fontsize=14, fontname='Times New Roman')
            plt.axis('off')
            plt.show()

            # 创建入口和出口
            # 左上角入口 (对应原始地图的左上角)
            inlet_y = extension_size  # 左上角
            inlet_x = extension_size - 10
            extended_map[inlet_y:extension_size+10, inlet_x:extension_size+10] = 0

            # 右下角出口 (对应原始地图的右下角)
            outlet_y = extension_size + h  # 右下角
            outlet_x = extension_size + w - 10
            extended_map[outlet_y:outlet_y+10, outlet_x:outlet_x+10] = 0

            # 统一的入口出口位置（用于流体模拟）- 使用(x,y)格式
            # 入口：左上角入口区域的中心点
            unified_inlet = (extension_size+5, extension_size+5)  # (x=15, y=15) 左上角入口中心
            # 出口：右下角出口区域的中心点
            unified_outlet = (w + extension_size-5, h + extension_size-5)  # (x=261, y=261) 右下角出口中心

            # 定义LBM边界线 - 使用(x,y)格式
            # 入口边界线（左上角区域的边界）- 垂直线段
            inlet_boundary_line = [
                (extension_size-5, extension_size),      # 起点 (x=10, y=10)
                (extension_size-5, extension_size+10)     # 终点 (x=10, y=20)
            ]

            # 出口边界线（右下角区域的边界）- 水平线段
            outlet_boundary_line = [
                (w + extension_size, h + extension_size+5),    # 起点 (x=266, y=266)
                (w + extension_size-10, h + extension_size+5)   # 终点 (x=256, y=266)
            ]

            print(f"  扩展地图形状: {extended_map.shape}")
            print(f"  边界扩展大小: {extension_size}")
            print(f"  统一入口位置: {unified_inlet} (左上角) - (x,y)格式")
            print(f"  统一出口位置: {unified_outlet} (右下角) - (x,y)格式")
            print(f"  入口边界线: {inlet_boundary_line} - (x,y)格式")
            print(f"  出口边界线: {outlet_boundary_line} - (x,y)格式")
            print("  ✅ 海冰地图加载成功")

            plt.figure(figsize=(10, 10), dpi=500)
            plt.imshow(extended_map, cmap='gray_r', origin='upper')

            # 绘制入口和出口中心点 - 注意：matplotlib使用(x,y)格式，与我们的坐标一致
            plt.scatter(unified_inlet[0], unified_inlet[1], color='green', s=200, marker='o', label='Inlet Center')
            plt.scatter(unified_outlet[0], unified_outlet[1], color='blue', s=200, marker='x', label='Outlet Center')

            # 绘制入口边界线 - 使用(x,y)格式
            inlet_x_coords = [inlet_boundary_line[0][0], inlet_boundary_line[1][0]]
            inlet_y_coords = [inlet_boundary_line[0][1], inlet_boundary_line[1][1]]
            plt.plot(inlet_x_coords, inlet_y_coords, color='green', linewidth=2, label='Inlet Boundary', alpha=0.8)

            # 绘制出口边界线 - 使用(x,y)格式
            outlet_x_coords = [outlet_boundary_line[0][0], outlet_boundary_line[1][0]]
            outlet_y_coords = [outlet_boundary_line[0][1], outlet_boundary_line[1][1]]
            plt.plot(outlet_x_coords, outlet_y_coords, color='blue', linewidth=2, label='Outlet Boundary', alpha=0.8)

            plt.title("Extended Map with LBM Inlet/Outlet Boundaries", fontsize=14, fontname='Times New Roman')
            plt.legend(loc='upper right')
            plt.axis('off')
            plt.show()

            # 返回地图数据，使用统一的入口出口位置
            return {
                'original_map': original_map,
                'extended_map': extended_map,
                'extension_size': extension_size,
                'start_point': unified_inlet,   # 统一的入口位置 (左上角)
                'end_point': unified_outlet,    # 统一的出口位置 (右下角)
                'original_shape': (h, w),
                'inlet_region': (inlet_y, inlet_x, extension_size+10, extension_size+10),  # 入口区域
                'outlet_region': (outlet_y, outlet_x, outlet_y+10, outlet_x+10),  # 出口区域
                'inlet_boundary_line': inlet_boundary_line,   # 入口边界线
                'outlet_boundary_line': outlet_boundary_line  # 出口边界线
            }

        except FileNotFoundError:
            print(f"❌ 错误: 找不到文件 {file_path}")
            return None
        except Exception as e:
            print(f"❌ 加载海冰地图时出错: {e}")
            return None

    @staticmethod
    def save_results_to_excel(data: np.ndarray, file_path: str, sheet_name: str = 'Sheet1') -> None:
        """
        保存结果到Excel文件

        参数:
        data: 要保存的数据
        file_path: 保存路径
        sheet_name: 工作表名称
        """
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            df = pd.DataFrame(data)
            df.to_excel(file_path, sheet_name=sheet_name, index=False, header=False)
            print(f"结果已保存至: {file_path}")
        except Exception as e:
            print(f"保存Excel文件时出错: {e}")

    @staticmethod
    def create_sample_grid(size: Tuple[int, int], obstacle_density: float = 0.3) -> np.ndarray:
        """
        创建示例网格地图

        参数:
        size: 网格大小 (height, width)
        obstacle_density: 障碍物密度

        返回:
        网格地图
        """
        h, w = size
        grid = np.zeros((h, w), dtype=int)

        # 随机添加障碍物
        np.random.seed(42)
        obstacle_mask = np.random.random((h, w)) < obstacle_density
        grid[obstacle_mask] = 1

        # 确保边界附近有通道
        grid[0, :] = 0
        grid[-1, :] = 0
        grid[:, 0] = 0
        grid[:, -1] = 0

        # 添加一些结构化障碍物
        # 垂直墙
        if w > 10:
            wall_x = w // 3
            wall_start = h // 4
            wall_end = 3 * h // 4
            grid[wall_start:wall_end, wall_x] = 1

            # 在墙上开个口
            gap_start = wall_start + (wall_end - wall_start) // 3
            gap_end = gap_start + (wall_end - wall_start) // 6
            grid[gap_start:gap_end, wall_x] = 0

        return grid


class PerformanceAnalyzer:
    """性能分析工具类"""

    def __init__(self):
        """初始化性能分析器"""
        self.timers = {}
        self.metrics = {}

    def start_timer(self, name: str) -> None:
        """开始计时"""
        self.timers[name] = time.time()

    def end_timer(self, name: str) -> float:
        """结束计时并返回耗时"""
        if name in self.timers:
            elapsed = time.time() - self.timers[name]
            self.metrics[f"{name}_time"] = elapsed
            return elapsed
        return 0.0

    def calculate_path_metrics(self, path: List[Tuple[int, int]],
                             speed_field: np.ndarray) -> Dict[str, float]:
        """
        计算路径性能指标

        参数:
        path: 路径点列表
        speed_field: 速度场

        返回:
        性能指标字典
        """
        if not path or len(path) < 2:
            return {}

        path_array = np.array(path)

        # 路径长度
        path_length = len(path)

        # 欧几里得距离
        euclidean_length = np.sum(np.sqrt(np.sum(np.diff(path_array, axis=0)**2, axis=1)))

        # 路径平滑度（曲率）
        if len(path) > 2:
            # 计算路径的二阶导数作为曲率的近似
            second_diff = np.diff(path_array, n=2, axis=0)
            curvature = np.mean(np.sqrt(np.sum(second_diff**2, axis=1)))
        else:
            curvature = 0.0

        # 路径上的平均速度
        path_speeds = []
        for x, y in path:
            if 0 <= x < speed_field.shape[0] and 0 <= y < speed_field.shape[1]:
                path_speeds.append(speed_field[x, y])

        avg_speed = np.mean(path_speeds) if path_speeds else 0.0
        min_speed = np.min(path_speeds) if path_speeds else 0.0
        max_speed = np.max(path_speeds) if path_speeds else 0.0

        # 路径效率（直线距离/实际路径长度）
        if len(path) >= 2:
            straight_line_distance = np.sqrt((path[-1][0] - path[0][0])**2 +
                                           (path[-1][1] - path[0][1])**2)
            efficiency = straight_line_distance / euclidean_length if euclidean_length > 0 else 0
        else:
            efficiency = 0.0

        return {
            'path_length': path_length,
            'euclidean_length': euclidean_length,
            'curvature': curvature,
            'avg_speed': avg_speed,
            'min_speed': min_speed,
            'max_speed': max_speed,
            'efficiency': efficiency
        }

    def calculate_reynolds_number(self, u_max: float, characteristic_length: float,
                                tau: float) -> float:
        """
        计算雷诺数

        参数:
        u_max: 最大速度
        characteristic_length: 特征长度
        tau: 松弛时间

        返回:
        雷诺数
        """
        nu = (tau - 0.5) / 3.0  # LBM中的运动粘度
        return u_max * characteristic_length / nu

    def analyze_convergence(self, convergence_history: List[float]) -> Dict[str, float]:
        """
        分析收敛性能

        参数:
        convergence_history: 收敛历史

        返回:
        收敛分析结果
        """
        if not convergence_history:
            return {}

        # 收敛迭代次数
        convergence_iterations = len(convergence_history)

        # 最终误差
        final_error = convergence_history[-1]

        # 收敛速度（指数拟合）
        if len(convergence_history) > 10:
            # 取对数进行线性拟合
            log_errors = np.log(np.array(convergence_history) + 1e-15)
            iterations = np.arange(len(convergence_history))

            # 线性拟合 log(error) = a * iteration + b
            coeffs = np.polyfit(iterations, log_errors, 1)
            convergence_rate = -coeffs[0]  # 收敛速度
        else:
            convergence_rate = 0.0

        # 收敛稳定性（后半段的标准差）
        if len(convergence_history) > 20:
            second_half = convergence_history[len(convergence_history)//2:]
            stability = np.std(second_half)
        else:
            stability = np.std(convergence_history)

        return {
            'convergence_iterations': convergence_iterations,
            'final_error': final_error,
            'convergence_rate': convergence_rate,
            'stability': stability
        }

    def get_all_metrics(self) -> Dict[str, float]:
        """获取所有性能指标"""
        return self.metrics.copy()

    def reset_metrics(self) -> None:
        """重置所有指标"""
        self.timers.clear()
        self.metrics.clear()


class FluidAnalyzer:
    """流体场分析工具类"""

    @staticmethod
    def calculate_vorticity(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算涡量场

        涡量定义: ω = ∂v/∂x - ∂u/∂y
        使用中心差分格式计算偏导数

        参数:
        u, v: 速度分量 (u为x方向速度，v为y方向速度)

        返回:
        涡量场
        """
        vorticity = np.zeros_like(u)
        # 使用中心差分计算涡量: ω = ∂v/∂x - ∂u/∂y
        # ∂v/∂x: v在x方向的偏导数 (axis=1)
        # ∂u/∂y: u在y方向的偏导数 (axis=0)
        vorticity[1:-1, 1:-1] = (v[1:-1, 2:] - v[1:-1, :-2]) / 2.0 - (u[2:, 1:-1] - u[:-2, 1:-1]) / 2.0
        return vorticity

    @staticmethod
    def calculate_divergence(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算散度场

        参数:
        u, v: 速度分量

        返回:
        散度场
        """
        du_dx = np.gradient(u, axis=1)
        dv_dy = np.gradient(v, axis=0)
        return du_dx + dv_dy

    @staticmethod
    def calculate_stream_function(u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算流函数

        参数:
        u, v: 速度分量

        返回:
        流函数
        """
        # 简化的流函数计算（假设无旋流动）
        psi = np.zeros_like(u)

        # 积分v得到psi
        for i in range(1, psi.shape[0]):
            psi[i, :] = psi[i-1, :] + v[i, :]

        return psi

    @staticmethod
    def identify_recirculation_zones(u: np.ndarray, v: np.ndarray,
                                   threshold: float = 1e-6) -> np.ndarray:
        """
        识别回流区

        参数:
        u, v: 速度分量
        threshold: 速度阈值

        返回:
        回流区掩码
        """
        speed = np.sqrt(u**2 + v**2)
        vorticity = FluidAnalyzer.calculate_vorticity(u, v)

        # 低速且高涡量的区域可能是回流区
        recirculation_mask = (speed < threshold) & (np.abs(vorticity) > np.percentile(np.abs(vorticity), 75))

        return recirculation_mask
