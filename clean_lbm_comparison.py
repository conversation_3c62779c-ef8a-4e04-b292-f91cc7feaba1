"""
清洁的LBM算法对比程序 - 顺序执行版本
删除所有函数定义，采用顺序代码运行，便于清晰观看过程变量
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

import numpy as np
import time
from utils import DataProcessor, FluidAnalyzer
from fluid_path_planner import FluidSimulator
from clean_visualization import CleanFluidVisualization

print("🚀 开始清洁LBM算法对比 - 顺序执行版本")
print("=" * 80)
print("删除所有函数定义，采用顺序代码运行，便于清晰观看过程变量")
print()

# ==================== 初始化部分 ====================
print("🔧 初始化可视化器和算法参数...")

# 创建可视化器
visualizer = CleanFluidVisualization()

# 算法参数配置
algorithm_params = {
    'traditional_lbm': {
        'tau': 0.8,
        'inlet_velocity': 0.8,
        'max_iterations': 2000,
        'boundary_type': 'zou_he',
        'collision_model': 'bgk',
        'flow_direction_mode': 'perpendicular',
        'convergence_threshold': 1e-20,
    },
    'enhanced_lbm': {
        'tau': 0.8,
        'inlet_velocity': 0.8,
        'max_iterations': 2000,
        'boundary_type': 'zou_he',
        'collision_model': 'bgk',
        'flow_direction_mode': 'perpendicular',
        'convergence_threshold': 1e-20,
    }
}

print(f"✅ 初始化完成")
print(f"   传统LBM参数: tau={algorithm_params['traditional_lbm']['tau']}, velocity={algorithm_params['traditional_lbm']['inlet_velocity']}")
print(f"   增强LBM参数: tau={algorithm_params['enhanced_lbm']['tau']}, velocity={algorithm_params['enhanced_lbm']['inlet_velocity']}")
print()

# ==================== 加载地图数据 ====================
print("1️⃣ 加载海冰地图数据...")

# 尝试加载海冰地图数据
try:
    map_data = DataProcessor.load_sea_ice_map("sea_ice_419.xlsx")
    if map_data is not None:
        print("✅ 海冰地图加载成功")
        print(f"   原始地图形状: {map_data['original_shape']}")
        print(f"   扩展地图形状: {map_data['extended_map'].shape}")
        print(f"   起始点: {map_data['start_point']}")
        print(f"   终点: {map_data['end_point']}")
    else:
        print("⚠️ 海冰地图加载失败，创建测试网格")
        # 创建测试网格
        grid_map = np.zeros((256, 256), dtype=int)
        # 添加一些障碍物
        grid_map[100:150, 120:140] = 1  # 中央障碍物
        grid_map[50:80, 50:80] = 1      # 左上障碍物
        grid_map[180:210, 180:210] = 1  # 右下障碍物

        map_data = {
            'original_map': grid_map,
            'extended_map': grid_map,
            'extension_size': 0,
            'start_point': (15, 15),
            'end_point': (240, 240),
            'original_shape': grid_map.shape,
            'inlet_boundary_line': [(10, 10), (10, 20)],
            'outlet_boundary_line': [(245, 245), (245, 255)]
        }
        print("✅ 测试网格创建完成")

except Exception as e:
    print(f"❌ 地图加载错误: {e}")
    print("   创建测试网格...")
    # 创建测试网格
    grid_map = np.zeros((256, 256), dtype=int)
    grid_map[100:150, 120:140] = 1  # 中央障碍物
    grid_map[50:80, 50:80] = 1      # 左上障碍物
    grid_map[180:210, 180:210] = 1  # 右下障碍物

    map_data = {
        'original_map': grid_map,
        'extended_map': grid_map,
        'extension_size': 0,
        'start_point': (15, 15),
        'end_point': (240, 240),
        'original_shape': grid_map.shape,
        'inlet_boundary_line': [(10, 10), (10, 20)],
        'outlet_boundary_line': [(245, 245), (245, 255)]
    }
    print("✅ 测试网格创建完成")

print()

# ==================== 运行传统LBM算法 ====================
print("2️⃣ 运行传统LBM算法...")

# 获取传统LBM参数
traditional_params = algorithm_params['traditional_lbm']
print(f"   参数: tau={traditional_params['tau']}, velocity={traditional_params['inlet_velocity']}")
print(f"   流动方向模式: {traditional_params['flow_direction_mode']}")

# 创建传统LBM模拟器
traditional_simulator = FluidSimulator(
    grid_map=map_data['original_map'],
    start_point=map_data['start_point'],
    end_point=map_data['end_point'],
    map_data=map_data
)

# 准备网格
traditional_simulator.prepare_grid_with_inlet_outlet()

# 运行传统LBM模拟
print("   开始传统LBM模拟...")
traditional_start_time = time.time()

traditional_simulator.run_fluid_simulation(
    max_iter=traditional_params['max_iterations'],
    tau=traditional_params['tau'],
    inlet_velocity=traditional_params['inlet_velocity'],
    convergence_threshold=traditional_params['convergence_threshold'],
    collision_model=traditional_params['collision_model'],
    boundary_condition=traditional_params['boundary_type'],
    flow_direction_mode=traditional_params['flow_direction_mode']
)

traditional_simulation_time = time.time() - traditional_start_time

# 提取传统LBM结果
traditional_results = {
    'simulator': traditional_simulator,
    'simulation_time': traditional_simulation_time,
    'u': traditional_simulator.u,
    'v': traditional_simulator.v,
    'rho': traditional_simulator.rho,
    'speed': traditional_simulator.speed,
    'vorticity': traditional_simulator.vorticity,
    'convergence_history': traditional_simulator.convergence_history,
    'grid_map': traditional_simulator.grid_map,
    'algorithm_name': 'traditional_lbm',
    'flow_direction_mode': traditional_params['flow_direction_mode'],
    'metrics': {
        'simulation_time': traditional_simulation_time,
        'final_max_velocity': np.max(traditional_simulator.speed) if traditional_simulator.speed is not None else 0,
        'velocity_smoothness': 1 / (1 + np.std(traditional_simulator.speed)) if traditional_simulator.speed is not None else 0,
        'avg_oscillation': 0.0  # 简化版本
    }
}

print(f"   ✅ 传统LBM完成，耗时: {traditional_simulation_time:.3f}秒")
print(f"   最大速度: {traditional_results['metrics']['final_max_velocity']:.6f}")
print()

# ==================== 运行增强LBM算法 ====================
print("3️⃣ 运行增强LBM算法...")

# 获取增强LBM参数
enhanced_params = algorithm_params['enhanced_lbm']
print(f"   参数: tau={enhanced_params['tau']}, velocity={enhanced_params['inlet_velocity']}")
print(f"   流动方向模式: {enhanced_params['flow_direction_mode']}")

# 创建增强LBM模拟器
enhanced_simulator = FluidSimulator(
    grid_map=map_data['original_map'],
    start_point=map_data['start_point'],
    end_point=map_data['end_point'],
    map_data=map_data
)

# 准备网格
enhanced_simulator.prepare_grid_with_inlet_outlet()

# 运行增强LBM模拟
print("   开始增强LBM模拟...")
enhanced_start_time = time.time()

enhanced_simulator.run_fluid_simulation(
    max_iter=enhanced_params['max_iterations'],
    tau=enhanced_params['tau'],
    inlet_velocity=enhanced_params['inlet_velocity'],
    convergence_threshold=enhanced_params['convergence_threshold'],
    collision_model=enhanced_params['collision_model'],
    boundary_condition=enhanced_params['boundary_type'],
    flow_direction_mode=enhanced_params['flow_direction_mode']
)

enhanced_simulation_time = time.time() - enhanced_start_time

# 提取增强LBM结果
enhanced_results = {
    'simulator': enhanced_simulator,
    'simulation_time': enhanced_simulation_time,
    'u': enhanced_simulator.u,
    'v': enhanced_simulator.v,
    'rho': enhanced_simulator.rho,
    'speed': enhanced_simulator.speed,
    'vorticity': enhanced_simulator.vorticity,
    'convergence_history': enhanced_simulator.convergence_history,
    'grid_map': enhanced_simulator.grid_map,
    'algorithm_name': 'enhanced_lbm',
    'flow_direction_mode': enhanced_params['flow_direction_mode'],
    'metrics': {
        'simulation_time': enhanced_simulation_time,
        'final_max_velocity': np.max(enhanced_simulator.speed) if enhanced_simulator.speed is not None else 0,
        'velocity_smoothness': 1 / (1 + np.std(enhanced_simulator.speed)) if enhanced_simulator.speed is not None else 0,
        'avg_oscillation': 0.0  # 简化版本
    }
}

print(f"   ✅ 增强LBM完成，耗时: {enhanced_simulation_time:.3f}秒")
print(f"   最大速度: {enhanced_results['metrics']['final_max_velocity']:.6f}")
print()

# ==================== 提取原始区域数据 ====================
print("4️⃣ 提取原始区域数据...")

# 定义提取原始区域的函数（内联）
def extract_original_region(data, map_data):
    """提取原始256x256区域的数据"""
    extension_size = map_data.get('extension_size', 0)
    if extension_size > 0:
        # 从扩展地图中提取中央的原始区域
        start_idx = extension_size
        end_idx = -extension_size
        return data[start_idx:end_idx, start_idx:end_idx]
    else:
        return data

# 传统LBM数据提取
print("   提取传统LBM原始区域数据...")
trad_u_orig = extract_original_region(traditional_results['u'], map_data)
trad_v_orig = extract_original_region(traditional_results['v'], map_data)
trad_rho_orig = extract_original_region(traditional_results['rho'], map_data)
trad_speed_orig = extract_original_region(traditional_results['speed'], map_data)
trad_vorticity_orig = FluidAnalyzer.calculate_vorticity(trad_u_orig, trad_v_orig)

print(f"   传统LBM原始区域尺寸: {trad_speed_orig.shape}")
print(f"   传统LBM最大速度: {np.max(trad_speed_orig):.6f}")

# 增强LBM数据提取
print("   提取增强LBM原始区域数据...")
enh_u_orig = extract_original_region(enhanced_results['u'], map_data)
enh_v_orig = extract_original_region(enhanced_results['v'], map_data)
enh_rho_orig = extract_original_region(enhanced_results['rho'], map_data)
enh_speed_orig = extract_original_region(enhanced_results['speed'], map_data)
enh_vorticity_orig = FluidAnalyzer.calculate_vorticity(enh_u_orig, enh_v_orig)

print(f"   增强LBM原始区域尺寸: {enh_speed_orig.shape}")
print(f"   增强LBM最大速度: {np.max(enh_speed_orig):.6f}")
print()

# ==================== 生成单图可视化 ====================
print("5️⃣ 生成单图可视化...")

# 创建输出目录
output_dir = "清洁可视化结果"
os.makedirs(output_dir, exist_ok=True)
print(f"   输出目录: {output_dir}")

# 生成传统LBM单图
print("   生成传统LBM单图...")
trad_dir = os.path.join(output_dir, "传统LBM单图")
trad_files = visualizer.create_single_field_visualizations(
    map_data=map_data,
    u=trad_u_orig,
    v=trad_v_orig,
    rho=trad_rho_orig,
    speed=trad_speed_orig,
    vorticity=trad_vorticity_orig,
    output_dir=trad_dir
)
print(f"   传统LBM单图生成完成: {len(trad_files)} 个文件")

# 生成增强LBM单图
print("   生成增强LBM单图...")
enh_dir = os.path.join(output_dir, "增强LBM单图")
enh_files = visualizer.create_single_field_visualizations(
    map_data=map_data,
    u=enh_u_orig,
    v=enh_v_orig,
    rho=enh_rho_orig,
    speed=enh_speed_orig,
    vorticity=enh_vorticity_orig,
    output_dir=enh_dir
)
print(f"   增强LBM单图生成完成: {len(enh_files)} 个文件")
print()

# ==================== 生成算法对比图 ====================
print("6️⃣ 生成算法对比图...")

# 创建对比图目录
comparison_dir = os.path.join(output_dir, "算法对比图")
print(f"   对比图目录: {comparison_dir}")

# 准备传统LBM数据
traditional_data = {
    'speed': trad_speed_orig,
    'vorticity': trad_vorticity_orig,
    'rho': trad_rho_orig,
    'grid_map': map_data['original_map'],
    'convergence_history': traditional_results['convergence_history'],
    'metrics': traditional_results['metrics']
}

# 准备增强LBM数据
enhanced_data = {
    'speed': enh_speed_orig,
    'vorticity': enh_vorticity_orig,
    'rho': enh_rho_orig,
    'grid_map': map_data['original_map'],
    'convergence_history': enhanced_results['convergence_history'],
    'metrics': enhanced_results['metrics']
}

# 生成算法对比图
comparison_files = visualizer.create_algorithm_comparison_plots(
    traditional_data=traditional_data,
    enhanced_data=enhanced_data,
    output_dir=comparison_dir
)
print(f"   算法对比图生成完成: {len(comparison_files)} 个文件")
print()

# ==================== 性能对比总结 ====================
print("7️⃣ 性能对比总结...")
print("=" * 60)

# 获取性能指标
trad_metrics = traditional_results['metrics']
enh_metrics = enhanced_results['metrics']

print(f"📊 算法性能对比:")
print(f"   传统LBM模拟时间: {trad_metrics['simulation_time']:.3f}秒")
print(f"   增强LBM模拟时间: {enh_metrics['simulation_time']:.3f}秒")

# 计算时间改进
if trad_metrics['simulation_time'] > 0:
    time_improvement = (trad_metrics['simulation_time'] - enh_metrics['simulation_time']) / trad_metrics['simulation_time'] * 100
    print(f"   时间改进: {time_improvement:.1f}%")

print(f"   传统LBM最大速度: {trad_metrics['final_max_velocity']:.6f}")
print(f"   增强LBM最大速度: {enh_metrics['final_max_velocity']:.6f}")

# 计算速度改进
if trad_metrics['final_max_velocity'] > 0:
    speed_improvement = (enh_metrics['final_max_velocity'] - trad_metrics['final_max_velocity']) / trad_metrics['final_max_velocity'] * 100
    print(f"   速度改进: {speed_improvement:.1f}%")

print(f"\n🧭 流动方向模式对比:")
print(f"   传统LBM: {traditional_results['flow_direction_mode']}")
print(f"   增强LBM: {enhanced_results['flow_direction_mode']}")

print(f"\n📁 生成的文件统计:")
print(f"   传统LBM单图: {len(trad_files)} 个文件")
print(f"   增强LBM单图: {len(enh_files)} 个文件")
print(f"   算法对比图: {len(comparison_files)} 个文件")
print(f"   总输出目录: {output_dir}")

print(f"\n🎉 清洁LBM算法对比完成！")
print("=" * 80)

# ==================== 最终总结 ====================
print(f"\n🎯 所有可视化文件已生成到: {output_dir}")
print("包含以下内容:")
print("   • 传统LBM单图: 速度场、速度X/Y分量、涡量场、压力场、流线场、三维图")
print("   • 增强LBM单图: 速度场、速度X/Y分量、涡量场、压力场、流线场、三维图")
print("   • 算法对比图: 收敛曲线对比、速度场对比、涡量场对比、压力场对比、性能指标对比")

print(f"\n💡 顺序执行版本特点:")
print("   ✅ 删除所有函数定义")
print("   ✅ 采用顺序代码运行")
print("   ✅ 便于观看过程变量")
print("   ✅ 清晰的执行步骤")
print("   ✅ 详细的进度输出")

print(f"\n🔍 关键过程变量:")
print(f"   map_data: 地图数据字典")
print(f"   traditional_results: 传统LBM结果字典")
print(f"   enhanced_results: 增强LBM结果字典")
print(f"   trad_speed_orig: 传统LBM速度场数组 {trad_speed_orig.shape}")
print(f"   enh_speed_orig: 增强LBM速度场数组 {enh_speed_orig.shape}")
print(f"   trad_files: 传统LBM可视化文件列表")
print(f"   enh_files: 增强LBM可视化文件列表")
print(f"   comparison_files: 对比图文件列表")

print(f"\n🎊 顺序执行LBM对比程序运行完成！")
