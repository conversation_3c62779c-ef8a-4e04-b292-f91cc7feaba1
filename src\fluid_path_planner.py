"""
流体模拟器主类
专注于LBM流体模拟和分析功能
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, List, Optional, Dict, Any
import time

try:
    from .lbm_core import LBMCore
    from .clean_visualization import CleanFluidVisualization
    from .utils import DataProcessor, PerformanceAnalyzer, FluidAnalyzer
except ImportError:
    from lbm_core import LBMCore
    from clean_visualization import CleanFluidVisualization
    from utils import DataProcessor, PerformanceAnalyzer, FluidAnalyzer


class FluidSimulator:
    """
    流体模拟器主类
    专注于LBM流体模拟和分析功能
    """

    def __init__(self, grid_map: np.ndarray,
                 start_point: Optional[Tuple[int, int]] = None,
                 end_point: Optional[Tuple[int, int]] = None,
                 map_data: Optional[Dict] = None):
        """
        初始化流体模拟器

        参数:
        grid_map: 网格地图，0表示流体，1表示障碍物
        start_point: 入口位置
        end_point: 出口位置
        map_data: 包含边界线信息的地图数据字典
        """
        self.original_grid = grid_map.copy()
        self.grid_map = None
        self.start_point = start_point
        self.end_point = end_point
        self.map_data = map_data

        # 从map_data中提取边界线信息
        self.inlet_boundary_line = None
        self.outlet_boundary_line = None
        if map_data is not None:
            self.inlet_boundary_line = map_data.get('inlet_boundary_line')
            self.outlet_boundary_line = map_data.get('outlet_boundary_line')
            # 如果start_point和end_point未指定，使用map_data中的值
            if self.start_point is None:
                self.start_point = map_data.get('start_point')
            if self.end_point is None:
                self.end_point = map_data.get('end_point')

        # 初始化组件
        self.lbm_core = None
        self.visualizer = CleanFluidVisualization()
        self.performance_analyzer = PerformanceAnalyzer()

        # 流体场数据
        self.u = None
        self.v = None
        self.rho = None
        self.speed = None
        self.vorticity = None
        self.convergence_history = []

        print(f"流体模拟器初始化完成")
        print(f"原始网格大小: {grid_map.shape}")
        if self.inlet_boundary_line:
            print(f"入口边界线: {self.inlet_boundary_line}")
        if self.outlet_boundary_line:
            print(f"出口边界线: {self.outlet_boundary_line}")

    def prepare_grid_with_inlet_outlet(self) -> None:
        """
        准备带有入口和出口的网格
        所有边界定义都来自utils.py中的load_sea_ice_map函数
        """
        print("准备网格边界...")

        # 必须有map_data，所有边界信息都来自utils.py
        if self.map_data is None or 'extended_map' not in self.map_data:
            raise ValueError("必须提供包含扩展地图的map_data，所有边界定义都应在utils.py中完成")

        # 直接使用utils.py中预处理的扩展地图
        self.grid_map = self.map_data['extended_map'].copy()
        print("✅ 使用utils.py中预处理的扩展地图")

        # 确保所有位置信息都已从utils.py传递过来
        if self.start_point is None or self.end_point is None:
            raise ValueError("入口和出口位置必须在utils.py中定义并传递")

        # 初始化LBM核心（启用并行计算）
        # 注意：grid_map.shape 是 (height, width) 即 (y_size, x_size)
        # 但LBMCore期望 (nx, ny) 即 (x_size, y_size)，所以需要转换
        height, width = self.grid_map.shape
        self.lbm_core = LBMCore((width, height), enable_parallel=True)  # (x_size, y_size)

        print(f"扩展后网格大小: {self.grid_map.shape}")
        print(f"LBM入口位置: {self.start_point}")
        print(f"LBM出口位置: {self.end_point}")

        # 打印从utils.py传递的边界线信息
        if self.inlet_boundary_line:
            print(f"LBM入口边界线: {self.inlet_boundary_line}")
        if self.outlet_boundary_line:
            print(f"LBM出口边界线: {self.outlet_boundary_line}")

    def run_fluid_simulation(self, max_iter: int = 5000, tau: float = 0.8,
                           inlet_velocity: float = 0.1, convergence_threshold: float = 1e-6,
                           collision_model: str = 'bgk', boundary_condition: str = 'zou_he',
                           flow_direction_mode: str = 'perpendicular') -> None:
        """
        运行LBM流体模拟

        参数:
        max_iter: 最大迭代次数
        tau: 松弛时间
        inlet_velocity: 入口速度
        convergence_threshold: 收敛阈值
        collision_model: 碰撞模型 ('bgk', 'mrt')
        boundary_condition: 边界条件类型
        flow_direction_mode: 流动方向模式
            - 'perpendicular': 垂直于入口边界线（默认）
            - 'point_to_point': 从起点到终点的方向
        """
        if self.lbm_core is None:
            raise ValueError("请先调用 prepare_grid_with_inlet_outlet() 方法")

        print("开始LBM流体模拟...")
        self.performance_analyzer.start_timer('fluid_simulation')

        # 初始化分布函数
        self.lbm_core.f = np.ones((self.lbm_core.q, self.lbm_core.nx, self.lbm_core.ny)) / self.lbm_core.q

        # 添加小的随机扰动
        np.random.seed(42)
        self.lbm_core.f += 0.01 * np.random.randn(self.lbm_core.q, self.lbm_core.nx, self.lbm_core.ny) / self.lbm_core.q

        # 主循环
        convergence_window = []
        window_size = 50

        for step in range(max_iter):
            # 保存上一步的速度场
            if step > 0:
                u_old = self.lbm_core.u.copy()
                v_old = self.lbm_core.v.copy()

            # 计算宏观量
            self.lbm_core.compute_macroscopic()

            # 应用边界条件，传递边界线信息、流动方向模式和矩形区域信息
            inlet_region = self.map_data.get('inlet_region') if self.map_data else None
            outlet_region = self.map_data.get('outlet_region') if self.map_data else None

            self.lbm_core.apply_boundary_conditions(
                self.grid_map,
                inlet_velocity,
                boundary_condition,
                self.start_point,
                self.end_point,
                self.inlet_boundary_line,
                self.outlet_boundary_line,
                flow_direction_mode,
                inlet_region,
                outlet_region
            )

            # 障碍物处速度设为0
            obstacle_mask = (self.grid_map == 1)
            self.lbm_core.u[obstacle_mask] = 0
            self.lbm_core.v[obstacle_mask] = 0

            # 碰撞步骤
            if collision_model == 'bgk':
                self.lbm_core.collision_bgk(tau)
            elif collision_model == 'mrt':
                self.lbm_core.collision_mrt(tau)
            else:
                raise ValueError(f"不支持的碰撞模型: {collision_model}")

            # 流动步骤
            self.lbm_core.streaming()

            # 处理障碍物边界
            self.lbm_core.bounce_back(obstacle_mask)

            # 检查收敛性
            if step > 0:
                # 计算速度变化，添加数值稳定性检查
                u_diff = self.lbm_core.u - u_old
                v_diff = self.lbm_core.v - v_old

                # 清理NaN和Inf值
                u_diff = np.nan_to_num(u_diff, nan=0.0, posinf=0.0, neginf=0.0)
                v_diff = np.nan_to_num(v_diff, nan=0.0, posinf=0.0, neginf=0.0)

                velocity_diff = np.sqrt(np.mean(u_diff**2 + v_diff**2))

                # 确保velocity_diff是有限的
                if not np.isfinite(velocity_diff):
                    velocity_diff = 1.0  # 如果计算失败，设为较大值防止错误收敛

                self.convergence_history.append(velocity_diff)
                convergence_window.append(velocity_diff)

                # 保持窗口大小
                if len(convergence_window) > window_size:
                    convergence_window.pop(0)

                # 打印进度
                if step % 100 == 0:
                    if np.isfinite(velocity_diff):
                        print(f"迭代 {step}, 速度变化: {velocity_diff:.8f}")
                    else:
                        print(f"迭代 {step}, 速度变化: 数值不稳定")

                # 收敛判断
                if len(convergence_window) == window_size:
                    # 清理收敛窗口中的无效值
                    valid_window = [v for v in convergence_window if np.isfinite(v)]

                    if valid_window:
                        window_mean = np.mean(valid_window)
                        window_std = np.std(valid_window)

                        if (np.isfinite(window_mean) and np.isfinite(window_std) and
                            window_mean < convergence_threshold and
                            window_std < convergence_threshold/2 and step > 1000):
                            print(f"在第 {step} 次迭代后收敛")
                            break

        # 计算最终的流体场，添加数值稳定性检查
        self.u = np.nan_to_num(self.lbm_core.u.copy(), nan=0.0, posinf=0.0, neginf=0.0)
        self.v = np.nan_to_num(self.lbm_core.v.copy(), nan=0.0, posinf=0.0, neginf=0.0)
        self.rho = np.nan_to_num(self.lbm_core.rho.copy(), nan=1.0, posinf=1.0, neginf=1.0)
        self.speed = np.sqrt(self.u**2 + self.v**2)
        self.speed = np.nan_to_num(self.speed, nan=0.0, posinf=0.0, neginf=0.0)
        self.vorticity = FluidAnalyzer.calculate_vorticity(self.u, self.v)
        self.vorticity = np.nan_to_num(self.vorticity, nan=0.0, posinf=0.0, neginf=0.0)

        simulation_time = self.performance_analyzer.end_timer('fluid_simulation')

        # 计算雷诺数，添加数值稳定性检查
        characteristic_length = min(self.grid_map.shape) / 2
        max_speed = np.max(self.speed) if self.speed.size > 0 else 0.0
        reynolds_number = self.performance_analyzer.calculate_reynolds_number(
            max_speed, characteristic_length, tau)

        print(f"流体模拟完成，耗时: {simulation_time:.2f}秒")
        print(f"雷诺数: {reynolds_number:.2f}")

        # 分析收敛性能
        convergence_metrics = self.performance_analyzer.analyze_convergence(self.convergence_history)
        self.performance_analyzer.metrics.update(convergence_metrics)



    def visualize_fluid_fields(self, output_dir: str = "流体场可视化") -> Dict[str, str]:
        """
        可视化流体场 - 生成所有单图可视化

        参数:
        output_dir: 输出目录

        返回:
        生成的文件路径字典
        """
        if self.speed is None:
            raise ValueError("请先运行流体模拟")

        # 准备地图数据
        map_data = {
            'original_map': self.original_grid,
            'extension_size': self.map_data.get('extension_size', 0) if self.map_data else 0,
            'start_point': self.start_point,
            'end_point': self.end_point
        }

        # 提取原始区域的数据
        extension_size = map_data['extension_size']
        if extension_size > 0:
            # 从扩展地图中提取中央的原始区域
            u_orig = self.u[extension_size:-extension_size, extension_size:-extension_size]
            v_orig = self.v[extension_size:-extension_size, extension_size:-extension_size]
            rho_orig = self.rho[extension_size:-extension_size, extension_size:-extension_size]
            speed_orig = self.speed[extension_size:-extension_size, extension_size:-extension_size]
            vorticity_orig = self.vorticity[extension_size:-extension_size, extension_size:-extension_size]
        else:
            u_orig = self.u
            v_orig = self.v
            rho_orig = self.rho
            speed_orig = self.speed
            vorticity_orig = self.vorticity

        # 生成所有单图可视化
        return self.visualizer.create_single_field_visualizations(
            map_data=map_data,
            u=u_orig,
            v=v_orig,
            rho=rho_orig,
            speed=speed_orig,
            vorticity=vorticity_orig,
            output_dir=output_dir
        )

    def visualize_3d_fields(self, field_name: str = 'speed', output_dir: str = "三维可视化") -> str:
        """
        3D可视化流体场

        参数:
        field_name: 场类型 ('speed', 'pressure', 'vorticity')
        output_dir: 输出目录

        返回:
        生成的文件路径
        """
        if self.speed is None:
            raise ValueError("请先运行流体模拟")

        import os
        os.makedirs(output_dir, exist_ok=True)

        # 提取原始区域的数据
        extension_size = self.map_data.get('extension_size', 0) if self.map_data else 0
        if extension_size > 0:
            grid_map_orig = self.original_grid
            if field_name == 'speed':
                data_orig = self.speed[extension_size:-extension_size, extension_size:-extension_size]
            elif field_name == 'pressure':
                data_orig = self.rho[extension_size:-extension_size, extension_size:-extension_size]
            elif field_name == 'vorticity':
                data_orig = self.vorticity[extension_size:-extension_size, extension_size:-extension_size]
            else:
                raise ValueError(f"不支持的场类型: {field_name}")
        else:
            grid_map_orig = self.grid_map
            if field_name == 'speed':
                data_orig = self.speed
            elif field_name == 'pressure':
                data_orig = self.rho
            elif field_name == 'vorticity':
                data_orig = self.vorticity
            else:
                raise ValueError(f"不支持的场类型: {field_name}")

        # 生成3D可视化
        save_path = os.path.join(output_dir, f"三维{field_name}场.png")

        if field_name in ['speed', 'vorticity']:
            return self.visualizer.create_3d_velocity_plot(data_orig, grid_map_orig, save_path)
        else:  # pressure
            return self.visualizer.create_3d_pressure_plot(data_orig, grid_map_orig, save_path)

    def save_results(self, base_path: str = "results") -> None:
        """保存流体模拟结果"""
        import os
        os.makedirs(base_path, exist_ok=True)

        # 保存速度场数据
        if self.speed is not None:
            DataProcessor.save_results_to_excel(
                self.speed, f"{base_path}/velocity_field.xlsx", "Speed")
            DataProcessor.save_results_to_excel(
                self.u, f"{base_path}/u_velocity.xlsx", "U_Velocity")
            DataProcessor.save_results_to_excel(
                self.v, f"{base_path}/v_velocity.xlsx", "V_Velocity")
            DataProcessor.save_results_to_excel(
                self.rho, f"{base_path}/pressure_field.xlsx", "Pressure")

        print(f"流体模拟结果已保存到: {base_path}")

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能总结"""
        summary = {
            'grid_size': self.grid_map.shape if self.grid_map is not None else None,
            'inlet_position': self.start_point,
            'outlet_position': self.end_point,
            'system_metrics': self.performance_analyzer.get_all_metrics()
        }

        return summary
