#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的LBM演示程序
完全按照参考代码风格实现，删除所有矩形区域和边界线设置
"""

import os
import sys
import numpy as np
from typing import Dict, Any

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.utils import DataProcessor
    from src.fluid_path_planner import FluidSimulator
    from src.clean_visualization import CleanFluidVisualization
except ImportError:
    from utils import DataProcessor
    from fluid_path_planner import FluidSimulator
    from clean_visualization import CleanFluidVisualization

class SimplifiedLBMDemo:
    """简化的LBM演示类"""

    def __init__(self, output_dir: str = "result"):
        """初始化演示程序"""
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        print("🚀 简化LBM演示程序")
        print("=" * 60)

    def run_demo(self) -> Dict[str, Any]:
        """运行完整的演示"""

        # 1. 加载海冰地图数据
        print("📍 加载海冰地图数据...")
        map_data = DataProcessor.load_sea_ice_map('sea_ice_419.xlsx')
        if map_data is None:
            raise ValueError("无法加载海冰地图数据")

        print("✅ 地图加载成功")
        print(f"   原始地图: {map_data['original_shape']}")
        print(f"   扩展地图: {map_data['extended_map'].shape}")
        print(f"   入口位置: {map_data['start_point']}")
        print(f"   出口位置: {map_data['end_point']}")

        # 2. 运行LBM流体模拟
        print("\n🌊 运行LBM流体模拟...")

        # 初始化流体模拟器
        planner = FluidSimulator(
            grid_map=map_data['extended_map'],
            start_point=map_data['start_point'],
            end_point=map_data['end_point'],
            map_data=map_data
        )

        # 准备网格
        planner.prepare_grid_with_inlet_outlet()

        # 运行流体模拟（参考代码风格）
        algorithm_params = {
            'max_iterations': 1000,
            'tau': 0.7,
            'inlet_velocity': 0.05,
            'convergence_threshold': 1e-6,
            'collision_model': 'bgk',
            'boundary_type': 'zou_he',
            'flow_direction_mode': 'reference_style'  # 参考代码风格
        }

        planner.run_fluid_simulation(
            max_iter=algorithm_params['max_iterations'],
            tau=algorithm_params['tau'],
            inlet_velocity=algorithm_params['inlet_velocity'],
            convergence_threshold=algorithm_params['convergence_threshold'],
            collision_model=algorithm_params['collision_model'],
            boundary_condition=algorithm_params['boundary_type'],
            flow_direction_mode=algorithm_params['flow_direction_mode']
        )

        # 获取模拟结果
        results = {
            'u_field': planner.u,
            'v_field': planner.v,
            'rho_field': planner.rho,
            'speed_field': planner.speed,
            'vorticity_field': planner.vorticity,
            'convergence_history': planner.convergence_history
        }

        print("✅ LBM模拟完成")
        print(f"   模拟时间: {planner.performance_analyzer.metrics.get('fluid_simulation_time', 0):.2f}秒")
        print(f"   收敛迭代: {len(planner.convergence_history)}")
        print(f"   最大速度: {np.max(results['speed_field']):.6f}")
        print(f"   平均速度: {np.mean(results['speed_field']):.6f}")

        # 3. 生成可视化结果
        print("\n🎨 生成可视化结果...")
        generated_files = self._create_visualizations(map_data, results)

        print(f"✅ 生成了 {len(generated_files)} 个可视化文件")

        # 4. 保存总结报告
        self._save_summary_report(map_data, algorithm_params, results, generated_files)

        print("\n🎉 简化LBM演示完成！")
        print(f"\n📁 所有文件保存在: {self.output_dir}")
        print(f"\n📋 生成的文件包括:")
        for i, file_path in enumerate(generated_files, 1):
            filename = os.path.basename(file_path)
            print(f"   {i}. {filename}")

        print(f"\n💡 主要特点:")
        print(f"   - 完全按照参考代码的边界条件实现")
        print(f"   - 修正了速度方向设置")
        print(f"   - 删除了矩形区域和边界线的复杂设置")
        print(f"   - 简化了代码结构，保持核心功能")
        print(f"   - 提供了完整的可视化输出")

        return {
            'map_data': map_data,
            'algorithm_params': algorithm_params,
            'results': results,
            'generated_files': generated_files,
            'performance_metrics': planner.performance_analyzer.get_all_metrics()
        }

    def _create_visualizations(self, map_data: Dict[str, Any], results: Dict[str, Any]) -> list:
        """创建可视化图表"""
        visualizer = CleanFluidVisualization()
        generated_files = []

        # 准备可视化数据
        grid_map = map_data['extended_map']
        start_point = map_data['start_point']
        end_point = map_data['end_point']

        # 1. 速度场
        file_path = visualizer.create_speed_field_plot(
            grid_map, results['speed_field'], start_point, end_point,
            os.path.join(self.output_dir, "velocity_field.png")
        )
        generated_files.append(file_path)

        # 2. 速度X分量
        file_path = visualizer.create_velocity_x_plot(
            grid_map, results['u_field'], start_point, end_point,
            os.path.join(self.output_dir, "velocity_x_component.png")
        )
        generated_files.append(file_path)

        # 3. 速度Y分量
        file_path = visualizer.create_velocity_y_plot(
            grid_map, results['v_field'], start_point, end_point,
            os.path.join(self.output_dir, "velocity_y_component.png")
        )
        generated_files.append(file_path)

        # 4. 涡量场
        file_path = visualizer.create_vorticity_plot(
            grid_map, results['vorticity_field'], start_point, end_point,
            os.path.join(self.output_dir, "vorticity_field.png")
        )
        generated_files.append(file_path)

        # 5. 压力场
        file_path = visualizer.create_pressure_plot(
            grid_map, results['rho_field'], start_point, end_point,
            os.path.join(self.output_dir, "pressure_field.png")
        )
        generated_files.append(file_path)

        # 6. 流线图
        file_path = visualizer.create_streamline_plot(
            grid_map, results['u_field'], results['v_field'], results['speed_field'],
            start_point, end_point, os.path.join(self.output_dir, "streamline_field.png")
        )
        generated_files.append(file_path)

        # 7. 3D速度场
        file_path = visualizer.create_3d_velocity_plot(
            results['speed_field'], grid_map,
            os.path.join(self.output_dir, "3d_velocity_field.png")
        )
        generated_files.append(file_path)

        # 8. 3D压力场
        file_path = visualizer.create_3d_pressure_plot(
            results['rho_field'], grid_map,
            os.path.join(self.output_dir, "3d_pressure_field.png")
        )
        generated_files.append(file_path)

        return generated_files

    def _save_summary_report(self, map_data: Dict[str, Any], algorithm_params: Dict[str, Any],
                           results: Dict[str, Any], generated_files: list) -> None:
        """保存总结报告"""
        report_path = os.path.join(self.output_dir, "simulation_summary.txt")

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("简化LBM海冰流体模拟总结报告\n")
            f.write("=" * 50 + "\n\n")

            f.write("1. 地图信息\n")
            f.write(f"   原始地图大小: {map_data['original_shape']}\n")
            f.write(f"   扩展地图大小: {map_data['extended_map'].shape}\n")
            f.write(f"   入口位置: {map_data['start_point']}\n")
            f.write(f"   出口位置: {map_data['end_point']}\n\n")

            f.write("2. 算法参数\n")
            for key, value in algorithm_params.items():
                f.write(f"   {key}: {value}\n")
            f.write("\n")

            f.write("3. 模拟结果\n")
            f.write(f"   收敛迭代次数: {len(results['convergence_history'])}\n")
            f.write(f"   最大速度: {np.max(results['speed_field']):.6f}\n")
            f.write(f"   平均速度: {np.mean(results['speed_field']):.6f}\n")
            f.write(f"   最大涡量: {np.max(np.abs(results['vorticity_field'])):.6f}\n\n")

            f.write("4. 生成的文件\n")
            for i, file_path in enumerate(generated_files, 1):
                filename = os.path.basename(file_path)
                f.write(f"   {i}. {filename}\n")

        print(f"📄 总结报告已保存: {report_path}")

def main():
    """主函数"""
    try:
        # 检查Numba可用性
        try:
            import numba
            print("✅ Numba JIT编译器可用，将启用高性能计算")
        except ImportError:
            print("⚠️ Numba不可用，将使用标准NumPy计算")

        # 运行演示
        demo = SimplifiedLBMDemo()
        results = demo.run_demo()

        return results

    except Exception as e:
        print(f"❌ 演示运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
