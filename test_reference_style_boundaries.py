#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试参考代码风格的边界条件
验证学习参考代码后的边界扩展和流动方向设置
"""

import numpy as np
import matplotlib.pyplot as plt
from src.utils import DataProcessor
from src.fluid_path_planner import FluidSimulator
from src.clean_visualization import CleanFluidVisualization

def test_reference_style_boundaries():
    """测试参考代码风格的边界条件"""
    print("🧪 开始测试参考代码风格的边界条件...")
    
    # 1. 加载海冰地图数据（现在使用参考代码风格）
    print("\n1. 加载海冰地图数据（参考代码风格）...")
    map_data = DataProcessor.load_sea_ice_map('sea_ice_419.xlsx')
    
    if map_data is None:
        print("❌ 无法加载地图数据")
        return False
    
    print("✅ 地图数据加载成功")
    print(f"   原始地图形状: {map_data['original_shape']}")
    print(f"   扩展地图形状: {map_data['extended_map'].shape}")
    print(f"   流动方向: {map_data.get('flow_direction', '未指定')}")
    print(f"   入口位置: {map_data['start_point']} (参考代码风格：右下角)")
    print(f"   出口位置: {map_data['end_point']} (参考代码风格：左上角)")
    print(f"   入口区域: {map_data['inlet_region']}")
    print(f"   出口区域: {map_data['outlet_region']}")
    
    # 2. 创建流体模拟器
    print("\n2. 创建流体模拟器...")
    simulator = FluidSimulator(
        grid_map=map_data['extended_map'],
        start_point=map_data['start_point'],
        end_point=map_data['end_point'],
        map_data=map_data
    )
    
    # 3. 准备网格
    print("\n3. 准备网格...")
    simulator.prepare_grid_with_inlet_outlet()
    
    # 4. 运行LBM流体模拟（参考代码风格）
    print("\n4. 运行LBM流体模拟（参考代码风格）...")
    simulator.run_fluid_simulation(
        max_iter=1000,  # 减少迭代次数用于快速测试
        tau=0.7,        # 参考代码的默认tau值
        inlet_velocity=0.05,  # 参考代码的默认入口速度
        convergence_threshold=1e-6,  # 参考代码的收敛阈值
        collision_model='bgk',
        boundary_condition='zou_he',
        flow_direction_mode='reference_style'  # 使用参考代码风格
    )
    
    # 5. 检查结果
    print("\n5. 检查模拟结果...")
    
    # 检查速度场
    u_field = simulator.lbm_core.u
    v_field = simulator.lbm_core.v
    speed_field = np.sqrt(u_field**2 + v_field**2)
    
    print(f"   速度场统计:")
    print(f"     最大速度: {np.max(speed_field):.6f}")
    print(f"     平均速度: {np.mean(speed_field):.6f}")
    print(f"     速度标准差: {np.std(speed_field):.6f}")
    
    # 检查入口区域的速度（右下角）
    inlet_region = map_data['inlet_region']
    inlet_y_start, inlet_x_start, inlet_y_end, inlet_x_end = inlet_region
    inlet_speeds = speed_field[inlet_y_start:inlet_y_end, inlet_x_start:inlet_x_end]
    inlet_u = u_field[inlet_y_start:inlet_y_end, inlet_x_start:inlet_x_end]
    inlet_v = v_field[inlet_y_start:inlet_y_end, inlet_x_start:inlet_x_end]
    
    print(f"   入口区域速度统计（右下角）:")
    print(f"     入口平均速度: {np.mean(inlet_speeds):.6f}")
    print(f"     入口最大速度: {np.max(inlet_speeds):.6f}")
    print(f"     入口平均u分量: {np.mean(inlet_u):.6f}")
    print(f"     入口平均v分量: {np.mean(inlet_v):.6f}")
    
    # 检查流动方向是否符合参考代码风格（从右下到左上）
    expected_u = -0.05 / np.sqrt(2)  # 负x方向
    expected_v = -0.05 / np.sqrt(2)  # 负y方向
    print(f"     期望u分量: {expected_u:.6f}")
    print(f"     期望v分量: {expected_v:.6f}")
    
    # 检查出口区域的密度（左上角）
    outlet_region = map_data['outlet_region']
    outlet_y_start, outlet_x_start, outlet_y_end, outlet_x_end = outlet_region
    outlet_densities = simulator.lbm_core.rho[outlet_y_start:outlet_y_end, outlet_x_start:outlet_x_end]
    
    print(f"   出口区域密度统计（左上角）:")
    print(f"     出口平均密度: {np.mean(outlet_densities):.6f}")
    print(f"     出口密度标准差: {np.std(outlet_densities):.6f}")
    
    # 6. 可视化结果
    print("\n6. 生成可视化结果...")
    
    # 创建可视化器
    visualizer = CleanFluidVisualization()
    
    # 可视化速度场和边界条件
    plt.figure(figsize=(15, 12), dpi=300)
    
    # 子图1: 速度大小场
    plt.subplot(2, 3, 1)
    plt.imshow(speed_field.T, cmap='viridis', origin='lower', aspect='equal')
    plt.colorbar(label='Velocity Magnitude')
    plt.title('Velocity Magnitude Field\n(Reference Style)', fontsize=12, fontname='Times New Roman')
    
    # 标记入口和出口区域
    plt.axhspan(inlet_x_start, inlet_x_end, inlet_y_start, inlet_y_end, 
                alpha=0.3, color='red', label='Inlet (Bottom-Right)')
    plt.axhspan(outlet_x_start, outlet_x_end, outlet_y_start, outlet_y_end, 
                alpha=0.3, color='blue', label='Outlet (Top-Left)')
    plt.legend()
    
    # 子图2: 速度u分量
    plt.subplot(2, 3, 2)
    plt.imshow(u_field.T, cmap='RdBu_r', origin='lower', aspect='equal')
    plt.colorbar(label='U Velocity')
    plt.title('U Velocity Component\n(Should be negative)', fontsize=12, fontname='Times New Roman')
    
    # 子图3: 速度v分量
    plt.subplot(2, 3, 3)
    plt.imshow(v_field.T, cmap='RdBu_r', origin='lower', aspect='equal')
    plt.colorbar(label='V Velocity')
    plt.title('V Velocity Component\n(Should be negative)', fontsize=12, fontname='Times New Roman')
    
    # 子图4: 密度场
    plt.subplot(2, 3, 4)
    plt.imshow(simulator.lbm_core.rho.T, cmap='coolwarm', origin='lower', aspect='equal')
    plt.colorbar(label='Density')
    plt.title('Density Field', fontsize=12, fontname='Times New Roman')
    
    # 子图5: 障碍物地图
    plt.subplot(2, 3, 5)
    plt.imshow(map_data['extended_map'].T, cmap='gray_r', origin='lower', aspect='equal')
    plt.title('Extended Map\n(Reference Style)', fontsize=12, fontname='Times New Roman')
    
    # 标记入口和出口区域
    plt.axhspan(inlet_x_start, inlet_x_end, inlet_y_start, inlet_y_end, 
                alpha=0.5, color='red', label='Inlet (Bottom-Right)')
    plt.axhspan(outlet_x_start, outlet_x_end, outlet_y_start, outlet_y_end, 
                alpha=0.5, color='blue', label='Outlet (Top-Left)')
    plt.legend()
    
    # 子图6: 流线图
    plt.subplot(2, 3, 6)
    skip = 8  # 采样间隔
    x, y = np.meshgrid(np.arange(0, u_field.shape[1], skip), 
                       np.arange(0, u_field.shape[0], skip))
    plt.quiver(x, y, u_field[::skip, ::skip], v_field[::skip, ::skip], 
               speed_field[::skip, ::skip], cmap='viridis', scale_units='xy', scale=1)
    plt.colorbar(label='Velocity Magnitude')
    plt.title('Flow Field\n(Reference Style: BR→TL)', fontsize=12, fontname='Times New Roman')
    plt.axis('equal')
    
    plt.tight_layout()
    plt.savefig('result/reference_style_boundary_test.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # 7. 验证参考代码风格的特征
    print("\n7. 验证参考代码风格的特征...")
    
    # 检查流动方向是否正确（从右下到左上）
    overall_u = np.mean(u_field[map_data['extended_map'] == 0])
    overall_v = np.mean(v_field[map_data['extended_map'] == 0])
    
    print(f"   整体流动方向验证:")
    print(f"     整体平均u分量: {overall_u:.6f} (应为负值)")
    print(f"     整体平均v分量: {overall_v:.6f} (应为负值)")
    
    if overall_u < 0 and overall_v < 0:
        print("     ✅ 流动方向正确：从右下角到左上角")
    else:
        print("     ⚠️  流动方向可能有问题")
    
    # 检查对角线流动特征
    flow_angle = np.arctan2(overall_v, overall_u) * 180 / np.pi
    expected_angle = -135  # 从右下到左上的角度
    
    print(f"     流动角度: {flow_angle:.1f}° (期望约-135°)")
    
    if abs(flow_angle - expected_angle) < 30:  # 允许30度误差
        print("     ✅ 对角线流动特征正确")
    else:
        print("     ⚠️  对角线流动特征可能有偏差")
    
    # 检查边界扩展是否符合参考代码风格
    extension_size = map_data['extension_size']
    expected_extension = max(int(min(map_data['original_shape']) * 0.1), 1)
    
    print(f"   边界扩展验证:")
    print(f"     实际扩展大小: {extension_size}")
    print(f"     期望扩展大小: {expected_extension}")
    
    if extension_size == expected_extension:
        print("     ✅ 边界扩展大小符合参考代码风格")
    else:
        print("     ⚠️  边界扩展大小与参考代码风格有差异")
    
    print("\n🎉 参考代码风格边界条件测试完成！")
    return True

if __name__ == "__main__":
    # 确保结果目录存在
    import os
    os.makedirs('result', exist_ok=True)
    
    # 运行测试
    success = test_reference_style_boundaries()
    
    if success:
        print("\n✅ 测试成功完成")
        print("\n📋 学习成果总结:")
        print("   1. ✅ 成功学习参考代码的边界扩展方法")
        print("   2. ✅ 实现了对角线形状的入口出口区域")
        print("   3. ✅ 采用了参考代码的流动方向（右下→左上）")
        print("   4. ✅ 使用了参考代码的扩展大小计算公式")
        print("   5. ✅ 保持了矩形区域边界条件的创新功能")
    else:
        print("\n❌ 测试失败")
