#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开放边界条件的LBM演示
"""

import os
import sys
import numpy as np
from typing import Dict, Any

# 添加src目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from src.utils import DataProcessor
    from src.fluid_path_planner import FluidSimulator
    from src.clean_visualization import CleanFluidVisualization
except ImportError:
    from utils import DataProcessor
    from fluid_path_planner import FluidSimulator
    from clean_visualization import CleanFluidVisualization

def test_open_boundary_lbm():
    """测试开放边界条件的LBM"""
    print("🧪 测试开放边界条件LBM")
    print("=" * 60)
    
    # 1. 加载海冰地图数据
    print("📍 加载海冰地图数据...")
    map_data = DataProcessor.load_sea_ice_map('sea_ice_419.xlsx')
    if map_data is None:
        raise ValueError("无法加载海冰地图数据")
    
    print("✅ 地图加载成功")
    print(f"   原始地图: {map_data['original_shape']}")
    print(f"   扩展地图: {map_data['extended_map'].shape}")
    print(f"   入口位置: {map_data['start_point']}")
    print(f"   出口位置: {map_data['end_point']}")
    
    # 2. 运行LBM流体模拟
    print("\n🌊 运行LBM流体模拟（开放边界条件）...")
    
    # 初始化流体模拟器
    simulator = FluidSimulator(
        grid_map=map_data['extended_map'],
        start_point=map_data['start_point'],
        end_point=map_data['end_point'],
        map_data=map_data
    )
    
    # 准备网格
    simulator.prepare_grid_with_inlet_outlet()
    
    # 运行流体模拟（使用开放边界条件）
    algorithm_params = {
        'max_iterations': 500,  # 减少迭代次数用于测试
        'tau': 0.7,
        'inlet_velocity': 0.03,  # 减小入口速度提高稳定性
        'convergence_threshold': 1e-6,
        'collision_model': 'bgk',
        'boundary_type': 'zou_he',
        'flow_direction_mode': 'reference_style'
    }
    
    print(f"   算法参数: {algorithm_params}")
    
    simulator.run_fluid_simulation(
        max_iter=algorithm_params['max_iterations'],
        tau=algorithm_params['tau'],
        inlet_velocity=algorithm_params['inlet_velocity'],
        convergence_threshold=algorithm_params['convergence_threshold'],
        collision_model=algorithm_params['collision_model'],
        boundary_condition=algorithm_params['boundary_type'],
        flow_direction_mode=algorithm_params['flow_direction_mode']
    )
    
    # 获取模拟结果
    results = {
        'u_field': simulator.u,
        'v_field': simulator.v,
        'rho_field': simulator.rho,
        'speed_field': simulator.speed,
        'vorticity_field': simulator.vorticity,
        'convergence_history': simulator.convergence_history
    }
    
    print("✅ LBM模拟完成")
    print(f"   模拟时间: {simulator.performance_analyzer.metrics.get('fluid_simulation_time', 0):.2f}秒")
    print(f"   收敛迭代: {len(simulator.convergence_history)}")
    print(f"   最大速度: {np.max(results['speed_field']):.6f}")
    print(f"   平均速度: {np.mean(results['speed_field']):.6f}")
    print(f"   最大密度: {np.max(results['rho_field']):.6f}")
    print(f"   最小密度: {np.min(results['rho_field']):.6f}")
    
    # 3. 生成可视化结果
    print("\n🎨 生成可视化结果...")
    os.makedirs('result', exist_ok=True)
    
    visualizer = CleanFluidVisualization()
    grid_map = map_data['extended_map']
    start_point = map_data['start_point']
    end_point = map_data['end_point']
    
    generated_files = []
    
    try:
        # 速度场
        file_path = visualizer.create_speed_field_plot(
            grid_map, results['speed_field'], start_point, end_point,
            'result/open_boundary_velocity_field.png'
        )
        generated_files.append(file_path)
        print(f"   ✅ 速度场: {file_path}")
        
        # 压力场
        file_path = visualizer.create_pressure_plot(
            grid_map, results['rho_field'], start_point, end_point,
            'result/open_boundary_pressure_field.png'
        )
        generated_files.append(file_path)
        print(f"   ✅ 压力场: {file_path}")
        
        # 流线图
        file_path = visualizer.create_streamline_plot(
            grid_map, results['u_field'], results['v_field'], results['speed_field'],
            start_point, end_point, 'result/open_boundary_streamline.png'
        )
        generated_files.append(file_path)
        print(f"   ✅ 流线图: {file_path}")
        
    except Exception as e:
        print(f"   ⚠️ 可视化部分失败: {e}")
    
    # 4. 数值稳定性分析
    print(f"\n📊 数值稳定性分析:")
    
    # 检查是否有NaN或Inf
    nan_count = np.sum(np.isnan(results['speed_field']))
    inf_count = np.sum(np.isinf(results['speed_field']))
    
    print(f"   NaN值数量: {nan_count}")
    print(f"   Inf值数量: {inf_count}")
    
    # 检查密度范围
    rho_min, rho_max = np.min(results['rho_field']), np.max(results['rho_field'])
    print(f"   密度范围: [{rho_min:.6f}, {rho_max:.6f}]")
    
    # 检查速度范围
    speed_max = np.max(results['speed_field'])
    print(f"   最大速度: {speed_max:.6f}")
    
    # 稳定性评估
    if nan_count == 0 and inf_count == 0 and rho_min > 0 and speed_max < 1.0:
        print(f"   ✅ 数值稳定性: 良好")
    else:
        print(f"   ⚠️ 数值稳定性: 存在问题")
    
    # 5. 边界条件效果分析
    print(f"\n🌊 边界条件效果分析:")
    
    # 检查入口边界
    inlet_speeds = []
    nx, ny = grid_map.shape
    
    # 右边界入口
    for i in range(nx):
        if grid_map[i, ny-1] == 0:
            inlet_speeds.append(results['speed_field'][i, ny-1])
    
    # 下边界入口
    for j in range(ny-1):
        if grid_map[nx-1, j] == 0:
            inlet_speeds.append(results['speed_field'][nx-1, j])
    
    if inlet_speeds:
        print(f"   入口平均速度: {np.mean(inlet_speeds):.6f}")
        print(f"   入口速度标准差: {np.std(inlet_speeds):.6f}")
    
    # 检查出口边界
    outlet_speeds = []
    
    # 左边界出口
    for i in range(nx):
        if grid_map[i, 0] == 0:
            outlet_speeds.append(results['speed_field'][i, 0])
    
    # 上边界出口
    for j in range(1, ny):
        if grid_map[0, j] == 0:
            outlet_speeds.append(results['speed_field'][0, j])
    
    if outlet_speeds:
        print(f"   出口平均速度: {np.mean(outlet_speeds):.6f}")
        print(f"   出口速度标准差: {np.std(outlet_speeds):.6f}")
    
    print(f"\n🎉 开放边界条件测试完成！")
    print(f"\n📁 生成的文件:")
    for i, file_path in enumerate(generated_files, 1):
        filename = os.path.basename(file_path)
        print(f"   {i}. {filename}")
    
    return {
        'algorithm_params': algorithm_params,
        'results': results,
        'generated_files': generated_files,
        'stability_metrics': {
            'nan_count': nan_count,
            'inf_count': inf_count,
            'rho_range': (rho_min, rho_max),
            'max_speed': speed_max
        }
    }

def main():
    """主函数"""
    try:
        # 检查Numba可用性
        try:
            import numba
            print("✅ Numba JIT编译器可用")
        except ImportError:
            print("⚠️ Numba不可用，将使用标准NumPy计算")
        
        # 运行测试
        results = test_open_boundary_lbm()
        
        print(f"\n💡 开放边界条件优势:")
        print(f"   - 更稳定的数值表现")
        print(f"   - 避免固定压力边界的不稳定性")
        print(f"   - 自然的流体流出")
        print(f"   - 减少数值振荡")
        
        return results
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    main()
