"""
LBM核心模块 - 格子玻尔兹曼方法的核心实现
包含改进的多松弛时间模型和优化的数值方法
"""

import numpy as np
import time
from scipy import ndimage
from typing import Tuple, Optional, Dict, Any, List
import os

# 尝试导入Numba进行JIT编译加速
try:
    from numba import jit, prange, njit
    NUMBA_AVAILABLE = True
    print("✅ Numba JIT编译器可用，将启用高性能计算")
except ImportError:
    NUMBA_AVAILABLE = False
    print("⚠️  Numba不可用，使用标准NumPy计算")
    # 定义空的装饰器
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    prange = range


class LBMCore:
    """
    改进的格子玻尔兹曼方法核心类
    实现多松弛时间（MRT）模型和自适应时间步长控制
    """

    def __init__(self, grid_shape: Tuple[int, int], model: str = 'D2Q9', enable_parallel: bool = True):
        """
        初始化LBM核心

        参数:
        grid_shape: 网格形状 (nx, ny)
        model: LBM模型类型，目前支持'D2Q9'
        enable_parallel: 是否启用并行计算
        """
        self.nx, self.ny = grid_shape
        self.model = model

        # 并行计算设置
        self.enable_parallel = enable_parallel
        import multiprocessing as mp
        self.num_cores = mp.cpu_count() if enable_parallel else 1

        # 设置NumPy线程数为最大CPU核心数
        if enable_parallel:
            os.environ['OMP_NUM_THREADS'] = str(self.num_cores)
            os.environ['MKL_NUM_THREADS'] = str(self.num_cores)
            os.environ['NUMEXPR_NUM_THREADS'] = str(self.num_cores)
            os.environ['OPENBLAS_NUM_THREADS'] = str(self.num_cores)
            os.environ['VECLIB_MAXIMUM_THREADS'] = str(self.num_cores)

            # 验证NumPy是否使用了多线程BLAS
            try:
                import numpy.distutils.system_info as sysinfo
                blas_info = sysinfo.get_info('blas')
                if blas_info:
                    print(f"   BLAS库: {blas_info.get('name', 'Unknown')}")
                else:
                    print("   BLAS库: 未检测到优化的BLAS库")
            except:
                print("   BLAS库: 无法检测")

        # 检测并行计算能力
        self.parallel_strategy = self._detect_parallel_capabilities()

        print(f"🚀 LBM并行计算设置: {'启用' if enable_parallel else '禁用'}")
        print(f"   CPU核心数: {self.num_cores}")
        print(f"   并行策略: {self.parallel_strategy}")

        # D2Q9模型参数
        if model == 'D2Q9':
            self._init_d2q9_model()
        else:
            raise ValueError(f"不支持的模型类型: {model}")

        # 初始化分布函数和宏观量
        self.f = np.ones((self.q, self.nx, self.ny), dtype=np.float64) / self.q
        self.rho = np.ones((self.nx, self.ny), dtype=np.float64)
        self.u = np.zeros((self.nx, self.ny), dtype=np.float64)
        self.v = np.zeros((self.nx, self.ny), dtype=np.float64)

        # 收敛监控
        self.convergence_history = []
        self.velocity_window = []

        # 并行计算的数据分块
        if self.enable_parallel and self.num_cores > 1:
            self._setup_parallel_chunks()

        # 预编译JIT函数（如果可用）
        if NUMBA_AVAILABLE and enable_parallel:
            self._warmup_jit_functions()

        # 运行性能基准测试
        if enable_parallel:
            self._run_performance_benchmark()

    def _detect_parallel_capabilities(self) -> str:
        """检测并选择最佳并行计算策略"""
        strategies = []

        # 检测Numba JIT
        if NUMBA_AVAILABLE:
            strategies.append("Numba JIT")

        # 检测BLAS库
        try:
            import numpy.distutils.system_info as sysinfo
            blas_info = sysinfo.get_info('blas')
            if blas_info:
                strategies.append(f"BLAS ({blas_info.get('name', 'Unknown')})")
        except:
            pass

        # 检测多进程能力
        if self.num_cores > 1:
            strategies.append(f"多进程 ({self.num_cores}核)")

        return " + ".join(strategies) if strategies else "串行计算"

    def _warmup_jit_functions(self):
        """预热JIT编译函数"""
        if not NUMBA_AVAILABLE:
            return

        print("   正在预编译JIT函数...")
        try:
            # 创建小型测试数据
            test_rho = np.ones((10, 10), dtype=np.float64)
            test_u = np.zeros((10, 10), dtype=np.float64)
            test_v = np.zeros((10, 10), dtype=np.float64)
            test_f = np.ones((9, 10, 10), dtype=np.float64) / 9

            # 预编译关键函数
            self._jit_compute_equilibrium(test_rho, test_u, test_v, self.w, self.c)
            self._jit_bgk_collision(test_f, test_rho, test_u, test_v, 0.8, self.w, self.c)

            print("   ✅ JIT函数预编译完成")
        except Exception as e:
            print(f"   ⚠️  JIT预编译失败: {e}")

    @staticmethod
    @njit(parallel=True, fastmath=True)
    def _jit_compute_equilibrium(rho, u, v, w, c):
        """JIT编译的平衡分布函数计算"""
        nx, ny = rho.shape
        q = len(w)
        feq = np.zeros((q, nx, ny), dtype=np.float64)

        for i in prange(nx):
            for j in prange(ny):
                usqr = u[i, j] * u[i, j] + v[i, j] * v[i, j]
                for k in range(q):
                    cu = c[k, 0] * u[i, j] + c[k, 1] * v[i, j]
                    feq[k, i, j] = w[k] * rho[i, j] * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)

        return feq

    @staticmethod
    @njit(parallel=True, fastmath=True)
    def _jit_bgk_collision(f, rho, u, v, tau, w, c):
        """JIT编译的BGK碰撞"""
        nx, ny = rho.shape
        q = len(w)
        inv_tau = 1.0 / tau

        for i in prange(nx):
            for j in prange(ny):
                usqr = u[i, j] * u[i, j] + v[i, j] * v[i, j]
                for k in range(q):
                    cu = c[k, 0] * u[i, j] + c[k, 1] * v[i, j]
                    feq = w[k] * rho[i, j] * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)
                    f[k, i, j] = f[k, i, j] - inv_tau * (f[k, i, j] - feq)

    @staticmethod
    @njit(parallel=True, fastmath=True)
    def _jit_compute_macroscopic(f, c):
        """JIT编译的宏观量计算"""
        q, nx, ny = f.shape
        rho = np.zeros((nx, ny), dtype=np.float64)
        u = np.zeros((nx, ny), dtype=np.float64)
        v = np.zeros((nx, ny), dtype=np.float64)

        # 计算密度
        for i in prange(nx):
            for j in prange(ny):
                for k in range(q):
                    rho[i, j] += f[k, i, j]

        # 计算速度
        for i in prange(nx):
            for j in prange(ny):
                if rho[i, j] > 1e-10:
                    for k in range(q):
                        u[i, j] += c[k, 0] * f[k, i, j]
                        v[i, j] += c[k, 1] * f[k, i, j]
                    u[i, j] /= rho[i, j]
                    v[i, j] /= rho[i, j]

        return rho, u, v

    def _run_performance_benchmark(self):
        """运行性能基准测试"""
        print("   正在运行性能基准测试...")

        # 创建测试数据
        test_size = min(100, self.nx // 4)  # 使用较小的测试尺寸
        test_f = np.random.random((self.q, test_size, test_size)).astype(np.float64)
        test_rho = np.random.random((test_size, test_size)).astype(np.float64) + 0.5
        test_u = np.random.random((test_size, test_size)).astype(np.float64) * 0.1
        test_v = np.random.random((test_size, test_size)).astype(np.float64) * 0.1

        # 测试不同策略的性能
        results = {}

        # 1. 测试串行BGK碰撞
        start_time = time.time()
        for _ in range(10):
            self._serial_bgk_collision_test(test_f.copy(), test_rho, test_u, test_v, 0.8)
        serial_time = (time.time() - start_time) / 10
        results['串行BGK'] = serial_time

        # 2. 测试JIT BGK碰撞（如果可用）
        if NUMBA_AVAILABLE:
            start_time = time.time()
            for _ in range(10):
                self._jit_bgk_collision(test_f.copy(), test_rho, test_u, test_v, 0.8, self.w, self.c)
            jit_time = (time.time() - start_time) / 10
            results['JIT BGK'] = jit_time

        # 3. 测试向量化BGK碰撞
        start_time = time.time()
        for _ in range(10):
            self._vectorized_bgk_collision_test(test_f.copy(), test_rho, test_u, test_v, 0.8)
        vectorized_time = (time.time() - start_time) / 10
        results['向量化BGK'] = vectorized_time

        # 计算加速比
        baseline = results['串行BGK']
        print("   性能基准测试结果:")
        for method, time_cost in results.items():
            speedup = baseline / time_cost if time_cost > 0 else 0
            print(f"     {method}: {time_cost*1000:.2f}ms (加速比: {speedup:.2f}x)")

        # 选择最佳策略
        best_method = min(results.items(), key=lambda x: x[1])
        print(f"   ✅ 最佳策略: {best_method[0]} ({best_method[1]*1000:.2f}ms)")

    def _serial_bgk_collision_test(self, f, rho, u, v, tau):
        """串行BGK碰撞测试"""
        inv_tau = 1.0 / tau
        usqr = u * u + v * v

        for i in range(self.q):
            cu = self.c[i, 0] * u + self.c[i, 1] * v
            feq = self.w[i] * rho * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)
            f[i] = f[i] - inv_tau * (f[i] - feq)

        return f

    def _vectorized_bgk_collision_test(self, f, rho, u, v, tau):
        """向量化BGK碰撞测试"""
        inv_tau = 1.0 / tau
        usqr = u * u + v * v

        feq = np.zeros_like(f)
        for i in range(self.q):
            cu = self.c[i, 0] * u + self.c[i, 1] * v
            feq[i] = self.w[i] * rho * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)

        f -= inv_tau * (f - feq)
        return f

    def _init_d2q9_model(self):
        """初始化D2Q9模型参数"""
        self.q = 9  # 速度方向数

        # 速度向量
        self.c = np.array([
            [0, 0],    # 静止
            [1, 0],    # 东
            [0, 1],    # 北
            [-1, 0],   # 西
            [0, -1],   # 南
            [1, 1],    # 东北
            [-1, 1],   # 西北
            [-1, -1],  # 西南
            [1, -1]    # 东南
        ])

        # 权重系数
        self.w = np.array([4/9, 1/9, 1/9, 1/9, 1/9, 1/36, 1/36, 1/36, 1/36])

        # 反向索引
        self.opposite = np.array([0, 3, 4, 1, 2, 7, 8, 5, 6])

        # MRT变换矩阵（多松弛时间模型）
        self._init_mrt_matrices()

    def _init_mrt_matrices(self):
        """初始化MRT变换矩阵"""
        # 简化的MRT变换矩阵
        self.M = np.array([
            [1, 1, 1, 1, 1, 1, 1, 1, 1],
            [-4, -1, -1, -1, -1, 2, 2, 2, 2],
            [4, -2, -2, -2, -2, 1, 1, 1, 1],
            [0, 1, 0, -1, 0, 1, -1, -1, 1],
            [0, -2, 0, 2, 0, 1, -1, -1, 1],
            [0, 0, 1, 0, -1, 1, 1, -1, -1],
            [0, 0, -2, 0, 2, 1, 1, -1, -1],
            [0, 1, -1, 1, -1, 0, 0, 0, 0],
            [0, 0, 0, 0, 0, 1, -1, 1, -1]
        ], dtype=np.float64)

        # 逆变换矩阵
        self.M_inv = np.linalg.inv(self.M)

        # 松弛参数向量（不同矩的不同松弛时间）
        self.s = np.array([0, 1.4, 1.4, 0, 1.2, 0, 1.2, 1.0, 1.0])

    def _setup_parallel_chunks(self):
        """设置并行计算的数据分块"""
        # 按行分块，每个核心处理一部分行
        chunk_size = max(1, self.nx // self.num_cores)
        self.chunks = []

        for i in range(self.num_cores):
            start_i = i * chunk_size
            end_i = min((i + 1) * chunk_size, self.nx)
            if start_i < self.nx:
                self.chunks.append((start_i, end_i))

        print(f"   数据分块: {len(self.chunks)} 个块，每块约 {chunk_size} 行")

    def equilibrium_bgk(self, rho: np.ndarray, u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算BGK平衡分布函数（向量化版本）

        参数:
        rho: 密度场
        u, v: 速度场分量

        返回:
        feq: 平衡分布函数
        """
        # 预计算平方项
        usqr = u**2 + v**2

        # 根据输入数据的形状初始化平衡分布函数数组
        if rho.ndim == 0:  # 标量
            feq = np.zeros(self.q)
        elif rho.ndim == 1:  # 一维数组
            feq = np.zeros((self.q, len(rho)))
        else:  # 二维数组
            feq = np.zeros((self.q, rho.shape[0], rho.shape[1]))

        # 向量化计算所有方向的平衡分布
        for i in range(self.q):
            # 计算速度点积 c_i·u
            cu = self.c[i, 0] * u + self.c[i, 1] * v
            # 平衡分布函数公式
            feq[i] = self.w[i] * rho * (1 + 3*cu + 4.5*cu**2 - 1.5*usqr)

        return feq

    def equilibrium_mrt(self, rho: np.ndarray, u: np.ndarray, v: np.ndarray) -> np.ndarray:
        """
        计算MRT平衡分布函数

        参数:
        rho: 密度场
        u, v: 速度场分量

        返回:
        feq: 平衡分布函数
        """
        # 计算平衡态矩
        meq = np.zeros((self.q, self.nx, self.ny))

        # 密度
        meq[0] = rho

        # 能量相关矩
        usqr = u**2 + v**2
        meq[1] = -2*rho + 3*rho*usqr
        meq[2] = rho - 3*rho*usqr

        # 动量
        meq[3] = rho * u
        meq[4] = -rho * u
        meq[5] = rho * v
        meq[6] = -rho * v

        # 应力张量
        meq[7] = rho * (u**2 - v**2)
        meq[8] = rho * u * v

        # 转换回分布函数空间
        feq = np.zeros((self.q, self.nx, self.ny))
        for i in range(self.q):
            for j in range(self.q):
                feq[i] += self.M_inv[i, j] * meq[j]

        return feq

    def _get_line_points(self, point1: Tuple[int, int], point2: Tuple[int, int]) -> List[Tuple[int, int]]:
        """
        获取两点之间的线段上的所有点（使用Bresenham算法）

        参数:
        point1: 起点 (x, y)
        point2: 终点 (x, y)

        返回:
        线段上的所有点列表 [(x, y), ...]
        """
        x1, y1 = point1
        x2, y2 = point2

        points = []

        # Bresenham直线算法
        dx = abs(x2 - x1)
        dy = abs(y2 - y1)

        x, y = x1, y1

        x_inc = 1 if x1 < x2 else -1
        y_inc = 1 if y1 < y2 else -1

        error = dx - dy

        while True:
            points.append((x, y))  # 返回(x, y)格式

            if x == x2 and y == y2:
                break

            error2 = 2 * error

            if error2 > -dy:
                error -= dy
                x += x_inc

            if error2 < dx:
                error += dx
                y += y_inc

        return points

    def _compute_line_normal(self, point1: Tuple[int, int], point2: Tuple[int, int]) -> Tuple[float, float]:
        """
        计算线段的法向量（垂直方向）

        参数:
        point1: 起点 (x, y)
        point2: 终点 (x, y)

        返回:
        归一化的法向量 (nx, ny)，指向流体区域
        """
        x1, y1 = point1
        x2, y2 = point2

        # 计算线段方向向量
        dx = x2 - x1
        dy = y2 - y1

        # 计算法向量（垂直于线段）
        # 对于入口边界线 [(10,10), (10,20)]，dx=0, dy=10（垂直线段，x坐标相同）
        # 法向量应该是 (1, 0) 表示向右流动
        if abs(dx) < 1e-6:  # 垂直线段（x坐标相同）
            normal_x = 1.0 if dy > 0 else -1.0  # 向右或向左
            normal_y = 0.0
        elif abs(dy) < 1e-6:  # 水平线段（y坐标相同）
            normal_x = 0.0
            normal_y = 1.0 if dx > 0 else -1.0  # 向下或向上
        else:  # 斜线段
            # 使用右手法则，选择合适的法向量方向
            normal_x = -dy
            normal_y = dx
            # 归一化
            length = np.sqrt(normal_x**2 + normal_y**2)
            if length > 0:
                normal_x /= length
                normal_y /= length

        # 对于入口边界线 [(10,10), (10,20)]，这是垂直线段，dx=0, dy=10
        # 应该返回 (1.0, 0.0) 表示向右流动
        return (normal_x, normal_y)  # 返回(nx, ny)格式

    def _compute_point_to_point_direction(self, start_point: Tuple[int, int],
                                        end_point: Tuple[int, int]) -> Tuple[float, float]:
        """
        计算从起点到终点的方向向量

        参数:
        start_point: 起点 (x, y)
        end_point: 终点 (x, y)

        返回:
        归一化的方向向量 (dx, dy)
        """
        x1, y1 = start_point
        x2, y2 = end_point

        # 计算方向向量
        dx = x2 - x1
        dy = y2 - y1

        # 归一化
        length = np.sqrt(dx**2 + dy**2)
        if length > 0:
            dx /= length
            dy /= length
        else:
            # 如果起点终点相同，默认向右
            dx, dy = 1.0, 0.0

        return (dx, dy)

    def collision_bgk(self, tau: float) -> None:
        """
        BGK碰撞算子（支持并行计算）

        参数:
        tau: 松弛时间
        """
        if self.enable_parallel:
            self._optimized_parallel_bgk_collision(tau)
        else:
            # 串行计算
            feq = self.equilibrium_bgk(self.rho, self.u, self.v)
            self.f = self.f - (1/tau) * (self.f - feq)

    def _optimized_parallel_bgk_collision(self, tau: float) -> None:
        """
        优化的并行BGK碰撞（使用JIT编译和多核心并行）

        根据可用的并行计算能力选择最佳策略
        """
        start_time = time.time()

        if NUMBA_AVAILABLE:
            # 使用JIT编译的高性能版本
            self._jit_bgk_collision(self.f, self.rho, self.u, self.v, tau, self.w, self.c)
            strategy = "Numba JIT"
        else:
            # 使用优化的NumPy向量化
            self._vectorized_bgk_collision(tau)
            strategy = "NumPy向量化"

        elapsed_time = time.time() - start_time

        # 记录性能信息（仅在第一次或每1000步记录一次）
        if not hasattr(self, '_bgk_performance_logged'):
            print(f"   BGK碰撞策略: {strategy}, 耗时: {elapsed_time*1000:.2f}ms")
            self._bgk_performance_logged = True

    def _vectorized_bgk_collision(self, tau: float) -> None:
        """向量化BGK碰撞（NumPy优化版本）"""
        inv_tau = 1.0 / tau

        # 使用NumPy的向量化操作
        usqr = self.u * self.u + self.v * self.v

        # 计算平衡分布函数（向量化）
        feq = np.zeros_like(self.f)
        for i in range(self.q):
            cu = self.c[i, 0] * self.u + self.c[i, 1] * self.v
            feq[i] = self.w[i] * self.rho * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)

        # BGK碰撞（就地操作）
        self.f -= inv_tau * (self.f - feq)

    def _multiprocess_bgk_collision(self, tau: float) -> None:
        """多进程BGK碰撞（适用于大型数组）"""
        try:
            from multiprocessing import Pool
            import numpy as np

            # 分块处理
            chunk_size = max(1, self.nx // self.num_cores)
            chunks = []

            for i in range(0, self.nx, chunk_size):
                end_i = min(i + chunk_size, self.nx)
                chunks.append((i, end_i))

            # 准备数据
            args_list = []
            for start_i, end_i in chunks:
                args = (
                    self.f[:, start_i:end_i, :].copy(),
                    self.rho[start_i:end_i, :].copy(),
                    self.u[start_i:end_i, :].copy(),
                    self.v[start_i:end_i, :].copy(),
                    tau,
                    self.w,
                    self.c
                )
                args_list.append(args)

            # 并行处理
            with Pool(processes=self.num_cores) as pool:
                results = pool.map(self._bgk_collision_chunk_worker, args_list)

            # 收集结果
            for i, (start_i, end_i) in enumerate(chunks):
                self.f[:, start_i:end_i, :] = results[i]

        except Exception as e:
            print(f"多进程BGK碰撞失败，回退到向量化: {e}")
            self._vectorized_bgk_collision(tau)

    @staticmethod
    def _bgk_collision_chunk_worker(args):
        """BGK碰撞的多进程工作函数"""
        f_chunk, rho_chunk, u_chunk, v_chunk, tau, w, c = args

        inv_tau = 1.0 / tau
        usqr = u_chunk * u_chunk + v_chunk * v_chunk

        # 计算平衡分布函数
        feq = np.zeros_like(f_chunk)
        for i in range(len(w)):
            cu = c[i, 0] * u_chunk + c[i, 1] * v_chunk
            feq[i] = w[i] * rho_chunk * (1.0 + 3.0 * cu + 4.5 * cu * cu - 1.5 * usqr)

        # BGK碰撞
        f_chunk -= inv_tau * (f_chunk - feq)

        return f_chunk

    def _batch_parallel_bgk_collision(self, tau: float, inv_tau: float, usqr: np.ndarray) -> None:
        """
        分批并行BGK碰撞处理

        通过分批处理大型数组来优化内存访问和缓存效率
        """
        batch_size = min(self.num_cores * 1000, self.nx // 4)  # 动态批次大小

        for start_i in range(0, self.nx, batch_size):
            end_i = min(start_i + batch_size, self.nx)

            # 提取当前批次的数据
            rho_batch = self.rho[start_i:end_i, :]
            u_batch = self.u[start_i:end_i, :]
            v_batch = self.v[start_i:end_i, :]
            usqr_batch = usqr[start_i:end_i, :]

            # 计算平衡分布函数（向量化）
            feq_batch = self._compute_equilibrium_batch(rho_batch, u_batch, v_batch, usqr_batch)

            # BGK碰撞更新（就地操作）
            f_batch = self.f[:, start_i:end_i, :]
            np.subtract(f_batch, feq_batch, out=f_batch)
            np.multiply(f_batch, inv_tau, out=f_batch)
            np.add(feq_batch, f_batch, out=f_batch)

    def _compute_equilibrium_batch(self, rho: np.ndarray, u: np.ndarray,
                                  v: np.ndarray, usqr: np.ndarray) -> np.ndarray:
        """
        批量计算平衡分布函数（高度优化的向量化版本）
        """
        batch_shape = rho.shape
        feq = np.zeros((self.q,) + batch_shape, dtype=np.float64)

        # 使用预计算的权重和速度向量
        w = self.w.reshape(-1, 1, 1)  # 广播形状
        cx = self.c[:, 0].reshape(-1, 1, 1)
        cy = self.c[:, 1].reshape(-1, 1, 1)

        # 向量化计算所有方向的cu值
        cu = cx * u + cy * v  # 广播乘法

        # 向量化计算平衡分布函数
        # 使用NumPy的广播和向量化操作，这些会自动并行化
        cu_squared = cu * cu
        feq = w * rho * (1.0 + 3.0 * cu + 4.5 * cu_squared - 1.5 * usqr)

        return feq

    def collision_mrt(self, tau: float) -> None:
        """
        MRT碰撞算子（多松弛时间）

        参数:
        tau: 主要松弛时间
        """
        # 计算当前矩
        m = np.zeros((self.q, self.nx, self.ny))
        for i in range(self.q):
            for j in range(self.q):
                m[i] += self.M[i, j] * self.f[j]

        # 计算平衡态矩
        meq = np.zeros((self.q, self.nx, self.ny))
        usqr = self.u**2 + self.v**2

        meq[0] = self.rho
        meq[1] = -2*self.rho + 3*self.rho*usqr
        meq[2] = self.rho - 3*self.rho*usqr
        meq[3] = self.rho * self.u
        meq[4] = -self.rho * self.u
        meq[5] = self.rho * self.v
        meq[6] = -self.rho * self.v
        meq[7] = self.rho * (self.u**2 - self.v**2)
        meq[8] = self.rho * self.u * self.v

        # 松弛到平衡态
        for i in range(self.q):
            m[i] = m[i] - self.s[i] * (m[i] - meq[i])

        # 转换回分布函数空间
        self.f = np.zeros((self.q, self.nx, self.ny))
        for i in range(self.q):
            for j in range(self.q):
                self.f[i] += self.M_inv[i, j] * m[j]

    def streaming(self) -> None:
        """流动步骤（并行优化版本）"""
        if self.enable_parallel:
            self._parallel_streaming()
        else:
            # 串行版本
            for i in range(self.q):
                self.f[i] = np.roll(np.roll(self.f[i], self.c[i, 0], axis=0),
                                   self.c[i, 1], axis=1)

    def _parallel_streaming(self) -> None:
        """并行流动步骤"""
        # NumPy的roll操作已经是高度优化的，使用向量化处理
        # 对于大数组，分批处理可以提高缓存效率
        if self.nx * self.ny > 50000:
            # 大数组分批处理
            batch_size = max(1, self.q // self.num_cores)
            for start_i in range(0, self.q, batch_size):
                end_i = min(start_i + batch_size, self.q)
                for i in range(start_i, end_i):
                    if self.c[i, 0] != 0 or self.c[i, 1] != 0:  # 跳过静止方向
                        self.f[i] = np.roll(np.roll(self.f[i], self.c[i, 0], axis=0),
                                           self.c[i, 1], axis=1)
        else:
            # 小数组直接处理
            for i in range(1, self.q):  # 跳过静止方向
                self.f[i] = np.roll(np.roll(self.f[i], self.c[i, 0], axis=0),
                                   self.c[i, 1], axis=1)

    def compute_macroscopic(self) -> None:
        """计算宏观量（并行优化版本）"""
        if self.enable_parallel:
            self._parallel_compute_macroscopic()
        else:
            self._serial_compute_macroscopic()

    def _parallel_compute_macroscopic(self) -> None:
        """并行计算宏观量"""
        if NUMBA_AVAILABLE:
            # 使用JIT编译的高性能版本
            self.rho, self.u, self.v = self._jit_compute_macroscopic(self.f, self.c)
        else:
            # 使用NumPy的向量化操作
            self._vectorized_compute_macroscopic()

    def _vectorized_compute_macroscopic(self) -> None:
        """向量化计算宏观量"""
        # 密度计算（向量化求和）
        self.rho = np.sum(self.f, axis=0)

        # 速度计算（向量化点积）
        # 使用einsum进行高效的张量收缩，这会自动并行化
        self.u = np.einsum('i,ixy->xy', self.c[:, 0], self.f)
        self.v = np.einsum('i,ixy->xy', self.c[:, 1], self.f)

        # 防止除零错误（向量化操作）
        mask = (self.rho > 1e-10)
        np.divide(self.u, self.rho, out=self.u, where=mask)
        np.divide(self.v, self.rho, out=self.v, where=mask)

        # 对于密度过小的区域，设置速度为0
        self.u[~mask] = 0.0
        self.v[~mask] = 0.0

    def _serial_compute_macroscopic(self) -> None:
        """串行计算宏观量"""
        # 密度
        self.rho = np.sum(self.f, axis=0)

        # 速度
        self.u = np.zeros_like(self.rho)
        self.v = np.zeros_like(self.rho)

        for i in range(self.q):
            self.u += self.c[i, 0] * self.f[i]
            self.v += self.c[i, 1] * self.f[i]

        # 防止除零错误
        mask = (self.rho > 1e-10)
        self.u[mask] = self.u[mask] / self.rho[mask]
        self.v[mask] = self.v[mask] / self.rho[mask]

    def bounce_back(self, obstacle_mask: np.ndarray) -> None:
        """
        反弹边界条件（改进版本，防止流体渗透）

        参数:
        obstacle_mask: 障碍物掩码
        """
        # 1. 标准反弹边界条件
        for i in range(1, self.q):
            opp_i = self.opposite[i]
            # 在障碍物位置交换分布函数
            temp = self.f[i].copy()
            self.f[i, obstacle_mask] = self.f[opp_i, obstacle_mask]
            self.f[opp_i, obstacle_mask] = temp[obstacle_mask]

        # 2. 强制障碍物内部速度为零
        self.u[obstacle_mask] = 0.0
        self.v[obstacle_mask] = 0.0

        # 3. 强制障碍物内部密度为平均密度
        if np.any(~obstacle_mask):
            avg_rho = np.mean(self.rho[~obstacle_mask])
            self.rho[obstacle_mask] = avg_rho

        # 4. 重新设置障碍物内部的分布函数为平衡态
        obstacle_indices = np.where(obstacle_mask)
        if len(obstacle_indices[0]) > 0:
            rho_obstacle = self.rho[obstacle_indices]
            u_obstacle = self.u[obstacle_indices]  # 应该是0
            v_obstacle = self.v[obstacle_indices]  # 应该是0

            # 计算平衡分布函数
            feq_obstacle = self.equilibrium_bgk(rho_obstacle, u_obstacle, v_obstacle)
            for i in range(self.q):
                self.f[i][obstacle_indices] = feq_obstacle[i]

        # 5. 防止流体渗透到障碍物边界
        self._prevent_fluid_penetration(obstacle_mask)

    def _prevent_fluid_penetration(self, obstacle_mask: np.ndarray) -> None:
        """
        防止流体渗透到障碍物中（简化版本）

        参数:
        obstacle_mask: 障碍物掩码
        """
        # 1. 强制障碍物内部速度为零
        self.u[obstacle_mask] = 0.0
        self.v[obstacle_mask] = 0.0

        # 2. 强制障碍物内部密度为平均值
        fluid_mask = ~obstacle_mask
        if np.any(fluid_mask):
            avg_rho = np.mean(self.rho[fluid_mask])
            self.rho[obstacle_mask] = avg_rho

        # 3. 重新设置障碍物内部的分布函数为平衡态
        if np.any(obstacle_mask):
            obstacle_indices = np.where(obstacle_mask)
            rho_obstacle = self.rho[obstacle_indices]
            u_obstacle = np.zeros_like(rho_obstacle)  # 速度为0
            v_obstacle = np.zeros_like(rho_obstacle)  # 速度为0

            # 计算平衡分布函数
            feq_obstacle = self.equilibrium_bgk(rho_obstacle, u_obstacle, v_obstacle)
            for i in range(self.q):
                self.f[i][obstacle_indices] = feq_obstacle[i]

    def _get_obstacle_boundary(self, obstacle_mask: np.ndarray) -> np.ndarray:
        """获取障碍物边界"""
        from scipy import ndimage

        # 使用形态学操作检测边界
        eroded = ndimage.binary_erosion(obstacle_mask)
        boundary = obstacle_mask & (~eroded)

        return boundary

    def _enforce_density_continuity(self, obstacle_mask: np.ndarray) -> None:
        """强制密度场连续性"""
        from scipy import ndimage

        # 获取流体区域的平均密度
        fluid_mask = ~obstacle_mask
        if np.any(fluid_mask):
            avg_fluid_rho = np.mean(self.rho[fluid_mask])

            # 障碍物内部密度设为平均值
            self.rho[obstacle_mask] = avg_fluid_rho

            # 对边界附近进行平滑处理
            boundary_mask = self._get_obstacle_boundary(obstacle_mask)
            boundary_neighbors = ndimage.binary_dilation(boundary_mask) & fluid_mask

            if np.any(boundary_neighbors):
                # 对边界邻居进行轻微平滑
                rho_smooth = ndimage.gaussian_filter(self.rho, sigma=0.5)
                self.rho[boundary_neighbors] = 0.8 * self.rho[boundary_neighbors] + 0.2 * rho_smooth[boundary_neighbors]

    def apply_boundary_conditions(self, grid_map: np.ndarray,
                                inlet_velocity: float,
                                boundary_type: str = 'zou_he',
                                inlet_pos: Tuple[int, int] = None,
                                outlet_pos: Tuple[int, int] = None,
                                inlet_boundary_line: List[Tuple[int, int]] = None,
                                outlet_boundary_line: List[Tuple[int, int]] = None,
                                flow_direction_mode: str = 'perpendicular',
                                inlet_region: Tuple[int, int, int, int] = None,
                                outlet_region: Tuple[int, int, int, int] = None) -> None:
        """
        应用边界条件

        参数:
        grid_map: 网格地图
        inlet_velocity: 入口速度
        boundary_type: 边界条件类型
        inlet_pos: 入口位置 (x, y)
        outlet_pos: 出口位置 (x, y)
        inlet_boundary_line: 入口边界线 [(x1, y1), (x2, y2)]
        outlet_boundary_line: 出口边界线 [(x1, y1), (x2, y2)]
        flow_direction_mode: 流动方向模式
            - 'perpendicular': 垂直于入口边界线（默认）
            - 'point_to_point': 从起点到终点的方向
        inlet_region: 入口矩形区域 (y_start, x_start, y_end, x_end)
        outlet_region: 出口矩形区域 (y_start, x_start, y_end, x_end)
        """
        if boundary_type == 'zou_he':
            self._apply_zou_he_boundary(grid_map, inlet_velocity, inlet_pos, outlet_pos,
                                      inlet_boundary_line, outlet_boundary_line, flow_direction_mode,
                                      inlet_region, outlet_region)
        elif boundary_type == 'equilibrium':
            self._apply_equilibrium_boundary(grid_map, inlet_velocity, inlet_pos, outlet_pos,
                                           inlet_boundary_line, outlet_boundary_line,
                                           inlet_region, outlet_region)
        else:
            raise ValueError(f"不支持的边界条件类型: {boundary_type}")

    def _apply_zou_he_boundary(self, grid_map: np.ndarray, inlet_velocity: float,
                              inlet_pos: Tuple[int, int] = None,
                              outlet_pos: Tuple[int, int] = None,
                              inlet_boundary_line: List[Tuple[int, int]] = None,
                              outlet_boundary_line: List[Tuple[int, int]] = None,
                              flow_direction_mode: str = 'perpendicular',
                              inlet_region: Tuple[int, int, int, int] = None,
                              outlet_region: Tuple[int, int, int, int] = None) -> None:
        """应用Zou-He边界条件"""
        h, w = grid_map.shape

        # 优先使用矩形区域定义（如果提供）
        if inlet_region is not None and outlet_region is not None:
            # 只在第一次调用时打印信息
            if not hasattr(self, '_boundary_info_printed'):
                print("使用矩形区域定义的入口和出口")
                self._boundary_info_printed = True

            # 解析入口区域 (y_start, x_start, y_end, x_end)
            inlet_y_start, inlet_x_start, inlet_y_end, inlet_x_end = inlet_region

            # 解析出口区域 (y_start, x_start, y_end, x_end)
            outlet_y_start, outlet_x_start, outlet_y_end, outlet_x_end = outlet_region

            # 创建入口区域掩码
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            inlet_mask[inlet_y_start:inlet_y_end, inlet_x_start:inlet_x_end] = True
            inlet_mask = inlet_mask & (grid_map == 0)  # 只在流体区域应用

            # 创建出口区域掩码
            outlet_mask = np.zeros_like(grid_map, dtype=bool)
            outlet_mask[outlet_y_start:outlet_y_end, outlet_x_start:outlet_x_end] = True
            outlet_mask = outlet_mask & (grid_map == 0)  # 只在流体区域应用

            # 学习参考代码的流动方向设置
            if flow_direction_mode == 'point_to_point' and inlet_pos is not None and outlet_pos is not None:
                # 从入口中心到出口中心的方向
                inlet_center_x = (inlet_x_start + inlet_x_end) // 2
                inlet_center_y = (inlet_y_start + inlet_y_end) // 2
                outlet_center_x = (outlet_x_start + outlet_x_end) // 2
                outlet_center_y = (outlet_y_start + outlet_y_end) // 2

                dx = outlet_center_x - inlet_center_x
                dy = outlet_center_y - inlet_center_y
                length = np.sqrt(dx**2 + dy**2)

                if length > 0:
                    inlet_direction = (dx / length, dy / length)  # (u_direction, v_direction)
                else:
                    inlet_direction = (1.0, 0.0)  # 默认向右

                direction_description = "区域中心点到点"
            elif flow_direction_mode == 'reference_style':
                # 参考代码风格：从右下角到左上角的对角线流动
                inlet_direction = (-inlet_velocity / np.sqrt(2), -inlet_velocity / np.sqrt(2))  # 负方向
                direction_description = "参考代码风格对角线（右下到左上）"
            else:
                # 默认从左上角入口向右下角出口流动
                inlet_direction = (1.0, 1.0)  # 向右下方向
                length = np.sqrt(2.0)
                inlet_direction = (inlet_direction[0] / length, inlet_direction[1] / length)
                direction_description = "左上角到右下角对角线"

            # 设置入口速度
            self.u[inlet_mask] = inlet_velocity * inlet_direction[0]
            self.v[inlet_mask] = inlet_velocity * inlet_direction[1]
            self.rho[inlet_mask] = 1.0

            # 设置出口压力边界条件
            self.rho[outlet_mask] = 1.0

            # 只在第一次调用时打印详细信息
            if not hasattr(self, '_boundary_details_printed'):
                print(f"  入口区域: ({inlet_y_start}, {inlet_x_start}) 到 ({inlet_y_end}, {inlet_x_end})")
                print(f"  出口区域: ({outlet_y_start}, {outlet_x_start}) 到 ({outlet_y_end}, {outlet_x_end})")
                print(f"  流动方向: {direction_description}")
                print(f"  方向向量: ({inlet_direction[0]:.3f}, {inlet_direction[1]:.3f})")
                print(f"  入口点数: {np.sum(inlet_mask)}, 出口点数: {np.sum(outlet_mask)}")
                self._boundary_details_printed = True

                # 存储边界信息供可视化使用
                self.boundary_info = {
                    'inlet_region': inlet_region,
                    'outlet_region': outlet_region,
                    'inlet_direction': inlet_direction,
                    'direction_description': direction_description,
                    'inlet_mask': inlet_mask.copy(),
                    'outlet_mask': outlet_mask.copy()
                }

        elif inlet_boundary_line is not None and outlet_boundary_line is not None:
            # 使用精确的边界线定义（只在第一次打印）
            if not hasattr(self, '_boundary_info_printed'):
                print(f"使用精确边界线: 入口{inlet_boundary_line}, 出口{outlet_boundary_line}")
                self._boundary_info_printed = True

            # 创建入口边界掩码
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            inlet_points = self._get_line_points(inlet_boundary_line[0], inlet_boundary_line[1])
            for x, y in inlet_points:  # 现在返回(x, y)格式
                if 0 <= x < w and 0 <= y < h and grid_map[x, y] == 0:  # 数组访问改为[x, y]
                    inlet_mask[x, y] = True

            # 根据流动方向模式计算入口速度方向
            if flow_direction_mode == 'perpendicular':
                # 模式1：垂直于入口边界线
                inlet_direction = self._compute_line_normal(inlet_boundary_line[0], inlet_boundary_line[1])
                direction_description = "垂直于边界线"
            elif flow_direction_mode == 'point_to_point':
                # 模式2：从起点到终点的方向
                if inlet_pos is not None and outlet_pos is not None:
                    inlet_direction = self._compute_point_to_point_direction(inlet_pos, outlet_pos)
                    direction_description = "起点到终点"
                else:
                    # 回退到垂直模式
                    inlet_direction = self._compute_line_normal(inlet_boundary_line[0], inlet_boundary_line[1])
                    direction_description = "垂直于边界线（回退）"
                    print("⚠️ 缺少起点终点信息，回退到垂直模式")
            else:
                raise ValueError(f"不支持的流动方向模式: {flow_direction_mode}")

            # 设置入口速度
            self.u[inlet_mask] = inlet_velocity * inlet_direction[0]
            self.v[inlet_mask] = inlet_velocity * inlet_direction[1]

            # 只在第一次打印详细信息
            if not hasattr(self, '_boundary_details_printed'):
                print(f"入口边界设置: 流动方向模式={flow_direction_mode} ({direction_description})")
                print(f"  方向向量: {inlet_direction}")
                print(f"  速度设置: ({inlet_velocity * inlet_direction[0]:.3f}, {inlet_velocity * inlet_direction[1]:.3f})")
                self._boundary_details_printed = True

            # 创建出口边界掩码
            outlet_mask = np.zeros_like(grid_map, dtype=bool)
            outlet_points = self._get_line_points(outlet_boundary_line[0], outlet_boundary_line[1])
            for x, y in outlet_points:  # 现在返回(x, y)格式
                if 0 <= x < w and 0 <= y < h and grid_map[x, y] == 0:  # 数组访问改为[x, y]
                    outlet_mask[x, y] = True

            # 设置出口压力边界条件
            self.rho[outlet_mask] = 1.0

            # 只在第一次打印完成信息
            if not hasattr(self, '_boundary_complete_printed'):
                print(f"边界条件设置完成: 入口点数{np.sum(inlet_mask)}, 出口点数{np.sum(outlet_mask)}")
                self._boundary_complete_printed = True

        elif inlet_pos is not None and outlet_pos is not None:
            # 回退到点位置的周围区域方法
            print(f"使用点位置周围区域: 入口{inlet_pos}, 出口{outlet_pos}")
            inlet_y, inlet_x = inlet_pos
            outlet_y, outlet_x = outlet_pos

            # 入口边界条件（周围区域）
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            radius = 3
            for dy in range(-radius, radius+1):
                for dx in range(-radius, radius+1):
                    y, x = inlet_y + dy, inlet_x + dx
                    if 0 <= y < h and 0 <= x < w and grid_map[y, x] == 0:
                        inlet_mask[y, x] = True

            # 设置从左上角到右下角的速度方向
            self.u[inlet_mask] = inlet_velocity * 0.707  # 向右的速度分量 (cos(45°))
            self.v[inlet_mask] = inlet_velocity * 0.707  # 向下的速度分量 (sin(45°))

            # 出口边界条件（周围区域）
            outlet_mask = np.zeros_like(grid_map, dtype=bool)
            for dy in range(-radius, radius+1):
                for dx in range(-radius, radius+1):
                    y, x = outlet_y + dy, outlet_x + dx
                    if 0 <= y < h and 0 <= x < w and grid_map[y, x] == 0:
                        outlet_mask[y, x] = True

            self.rho[outlet_mask] = 1.0
        else:
            # 使用默认的边界条件（原有逻辑）
            extension_size = max(int(min(h, w) * 0.1), 1)

            # 入口边界条件（左下角区域，向右的速度）
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            inlet_mask[-1, :extension_size+1] = True  # 底部边界
            inlet_mask[-extension_size-1:, 0] = True  # 左侧边界
            inlet_mask = inlet_mask & (grid_map == 0)

            self.u[inlet_mask] = inlet_velocity  # 向右的速度
            self.v[inlet_mask] = 0.0  # 垂直速度为0

            # 出口边界条件（右上角区域）
            outlet_mask = np.zeros_like(grid_map, dtype=bool)
            outlet_mask[0, -extension_size-1:] = True  # 顶部边界
            outlet_mask[:extension_size+1, -1] = True  # 右侧边界
            outlet_mask = outlet_mask & (grid_map == 0)

            self.rho[outlet_mask] = 1.0

            # 零梯度速度条件
            # 出口处的速度梯度
            self.u[0, -extension_size-1:] = self.u[1, -extension_size-1:]
            self.v[0, -extension_size-1:] = self.v[1, -extension_size-1:]
            self.u[:extension_size+1, -1] = self.u[:extension_size+1, -2]
            self.v[:extension_size+1, -1] = self.v[:extension_size+1, -2]

    def _apply_equilibrium_boundary(self, grid_map: np.ndarray, inlet_velocity: float,
                                   inlet_pos: Tuple[int, int] = None,
                                   outlet_pos: Tuple[int, int] = None) -> None:
        """应用平衡态边界条件"""
        h, w = grid_map.shape

        if inlet_pos is not None and outlet_pos is not None:
            # 使用指定的入口出口位置
            inlet_y, inlet_x = inlet_pos
            outlet_y, outlet_x = outlet_pos

            # 入口边界条件（左上角，向右下方向的速度）
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            radius = 3
            for dy in range(-radius, radius+1):
                for dx in range(-radius, radius+1):
                    y, x = inlet_y + dy, inlet_x + dx
                    if 0 <= y < h and 0 <= x < w and grid_map[y, x] == 0:
                        inlet_mask[y, x] = True

            # 设置从左上角到右下角的速度方向
            self.u[inlet_mask] = inlet_velocity * 0.707  # 向右的速度分量 (cos(45°))
            self.v[inlet_mask] = inlet_velocity * 0.707  # 向下的速度分量 (sin(45°))
            self.rho[inlet_mask] = 1.0

            # 出口边界条件（指定位置周围区域）
            outlet_mask = np.zeros_like(grid_map, dtype=bool)
            for dy in range(-radius, radius+1):
                for dx in range(-radius, radius+1):
                    y, x = outlet_y + dy, outlet_x + dx
                    if 0 <= y < h and 0 <= x < w and grid_map[y, x] == 0:
                        outlet_mask[y, x] = True

            self.rho[outlet_mask] = 1.0
        else:
            # 使用默认的边界条件（原有逻辑）
            extension_size = max(int(min(h, w) * 0.1), 1)

            # 入口（左下角，向右的速度）
            inlet_mask = np.zeros_like(grid_map, dtype=bool)
            inlet_mask[-1, :extension_size+1] = True  # 底部边界
            inlet_mask[-extension_size-1:, 0] = True  # 左侧边界
            inlet_mask = inlet_mask & (grid_map == 0)

            self.u[inlet_mask] = inlet_velocity  # 向右的速度
            self.v[inlet_mask] = 0.0  # 垂直速度为0
            self.rho[inlet_mask] = 1.0

        # 重新计算入口处的分布函数
        if np.any(inlet_mask):
            inlet_indices = np.where(inlet_mask)
            rho_inlet = self.rho[inlet_indices]
            u_inlet = self.u[inlet_indices]
            v_inlet = self.v[inlet_indices]

            feq_inlet = self.equilibrium_bgk(rho_inlet, u_inlet, v_inlet)
            for i in range(self.q):
                self.f[i][inlet_indices] = feq_inlet[i]

    # ==================== 改进功能：多尺度障碍物处理 ====================

    def adaptive_time_stepping(self, target_cfl: float = 0.1) -> float:
        """
        自适应时间步长控制

        参数:
        target_cfl: 目标CFL数

        返回:
        optimal_tau: 优化的松弛时间
        """
        # 计算当前最大速度
        max_velocity = np.max(np.sqrt(self.u**2 + self.v**2))

        # 避免除零
        if max_velocity < 1e-10:
            max_velocity = 0.1

        # 计算CFL数
        cfl = max_velocity

        # 根据CFL数调整松弛时间
        if cfl > target_cfl:
            # 速度过高，增加粘性（增大tau）
            optimal_tau = 0.5 + (cfl / target_cfl) * 0.3
        else:
            # 速度适中，使用标准值
            optimal_tau = 0.8

        # 限制tau的范围以保证数值稳定性
        optimal_tau = np.clip(optimal_tau, 0.51, 2.0)

        return optimal_tau

    def multi_scale_boundary_treatment(self, obstacle_mask: np.ndarray) -> None:
        """
        多尺度障碍物边界处理

        参数:
        obstacle_mask: 障碍物掩码
        """
        # 1. 标准反弹边界条件
        self.bounce_back(obstacle_mask)

        # 2. 检测小尺度障碍物（单格点或小块）
        small_obstacles = self._detect_small_obstacles(obstacle_mask)

        # 3. 对小尺度障碍物应用改进的边界条件
        if np.any(small_obstacles):
            self._apply_improved_small_obstacle_bc(small_obstacles)

        # 4. 检测大尺度障碍物的边界
        large_obstacle_boundaries = self._detect_large_obstacle_boundaries(obstacle_mask)

        # 5. 对大尺度障碍物边界应用平滑处理
        if np.any(large_obstacle_boundaries):
            self._apply_smooth_boundary_treatment(large_obstacle_boundaries)

    def _detect_small_obstacles(self, obstacle_mask: np.ndarray) -> np.ndarray:
        """检测小尺度障碍物"""
        # 使用连通域分析检测小障碍物
        from scipy import ndimage

        labeled_obstacles, num_features = ndimage.label(obstacle_mask)
        small_obstacles = np.zeros_like(obstacle_mask, dtype=bool)

        for i in range(1, num_features + 1):
            obstacle_region = (labeled_obstacles == i)
            obstacle_size = np.sum(obstacle_region)

            # 定义小障碍物阈值（少于9个格点）
            if obstacle_size <= 9:
                small_obstacles |= obstacle_region

        return small_obstacles

    def _apply_improved_small_obstacle_bc(self, small_obstacles: np.ndarray) -> None:
        """对小尺度障碍物应用改进的边界条件"""
        # 对小障碍物使用插值边界条件而不是简单的反弹
        # 这可以减少数值振荡

        # 获取小障碍物的邻居点
        from scipy import ndimage

        # 膨胀操作获取边界邻居
        neighbor_mask = ndimage.binary_dilation(small_obstacles) & (~small_obstacles)

        # 对小障碍物周围应用平滑的速度场
        if np.any(neighbor_mask):
            # 计算邻居点的平均速度
            u_smooth = ndimage.gaussian_filter(self.u, sigma=0.5)
            v_smooth = ndimage.gaussian_filter(self.v, sigma=0.5)

            # 在小障碍物边界应用平滑速度
            self.u[small_obstacles] = 0
            self.v[small_obstacles] = 0

            # 重新计算这些位置的分布函数
            if np.any(small_obstacles):
                # 获取小障碍物位置的索引
                small_indices = np.where(small_obstacles)

                # 提取对应位置的宏观量
                rho_small = self.rho[small_indices]
                u_small = self.u[small_indices]
                v_small = self.v[small_indices]

                # 计算平衡分布函数
                feq_smooth = self.equilibrium_bgk(rho_small, u_small, v_small)

                # 更新分布函数
                for i in range(self.q):
                    self.f[i][small_indices] = feq_smooth[i]

    def _detect_large_obstacle_boundaries(self, obstacle_mask: np.ndarray) -> np.ndarray:
        """检测大尺度障碍物的边界"""
        from scipy import ndimage

        # 使用形态学操作检测边界
        eroded = ndimage.binary_erosion(obstacle_mask)
        boundaries = obstacle_mask & (~eroded)

        # 只保留大障碍物的边界
        labeled_obstacles, num_features = ndimage.label(obstacle_mask)
        large_boundaries = np.zeros_like(boundaries, dtype=bool)

        for i in range(1, num_features + 1):
            obstacle_region = (labeled_obstacles == i)
            obstacle_size = np.sum(obstacle_region)

            # 大障碍物阈值（超过25个格点）
            if obstacle_size > 25:
                large_boundaries |= (boundaries & obstacle_region)

        return large_boundaries

    def _apply_smooth_boundary_treatment(self, boundaries: np.ndarray) -> None:
        """对大尺度障碍物边界应用平滑处理"""
        # 对大障碍物边界应用更精确的边界条件
        # 使用二阶精度的边界处理

        from scipy import ndimage

        # 计算边界法向量
        boundary_normals = self._compute_boundary_normals(boundaries)

        # 应用法向量修正的反弹边界条件
        for i in range(1, self.q):
            opp_i = self.opposite[i]

            # 计算速度方向与法向量的点积
            dot_product = (self.c[i, 0] * boundary_normals[:, :, 0] +
                          self.c[i, 1] * boundary_normals[:, :, 1])

            # 只对指向障碍物内部的方向应用反弹
            inward_mask = boundaries & (dot_product > 0)

            if np.any(inward_mask):
                temp = self.f[i].copy()
                self.f[i, inward_mask] = self.f[opp_i, inward_mask]
                self.f[opp_i, inward_mask] = temp[inward_mask]

    def _compute_boundary_normals(self, boundaries: np.ndarray) -> np.ndarray:
        """计算边界法向量"""
        from scipy import ndimage

        # 使用梯度计算法向量
        boundary_float = boundaries.astype(float)

        # 计算梯度
        grad_x = ndimage.sobel(boundary_float, axis=1)
        grad_y = ndimage.sobel(boundary_float, axis=0)

        # 归一化
        magnitude = np.sqrt(grad_x**2 + grad_y**2)
        magnitude[magnitude == 0] = 1  # 避免除零

        normals = np.zeros((self.nx, self.ny, 2))
        normals[:, :, 0] = grad_x / magnitude
        normals[:, :, 1] = grad_y / magnitude

        return normals

    # ==================== 改进功能：不规则边界处理 ====================

    def curved_boundary_interpolation(self, obstacle_mask: np.ndarray) -> None:
        """
        曲线边界插值处理

        参数:
        obstacle_mask: 障碍物掩码
        """
        # 检测曲线边界
        curved_boundaries = self._detect_curved_boundaries(obstacle_mask)

        if np.any(curved_boundaries):
            # 应用插值边界条件
            self._apply_interpolated_boundary_conditions(curved_boundaries, obstacle_mask)

    def _detect_curved_boundaries(self, obstacle_mask: np.ndarray) -> np.ndarray:
        """检测曲线边界"""
        from scipy import ndimage

        # 计算边界曲率
        boundaries = self._get_boundary_points(obstacle_mask)
        curvature = self._compute_boundary_curvature(boundaries)

        # 高曲率区域被认为是曲线边界
        curved_threshold = 0.5
        curved_boundaries = boundaries & (curvature > curved_threshold)

        return curved_boundaries

    def _get_boundary_points(self, obstacle_mask: np.ndarray) -> np.ndarray:
        """获取边界点"""
        from scipy import ndimage

        # 使用形态学操作获取边界
        eroded = ndimage.binary_erosion(obstacle_mask)
        boundaries = obstacle_mask & (~eroded)

        return boundaries

    def _compute_boundary_curvature(self, boundaries: np.ndarray) -> np.ndarray:
        """计算边界曲率"""
        from scipy import ndimage

        # 使用二阶导数估计曲率
        boundary_float = boundaries.astype(float)

        # 计算一阶导数
        dx = ndimage.sobel(boundary_float, axis=1)
        dy = ndimage.sobel(boundary_float, axis=0)

        # 计算二阶导数
        dxx = ndimage.sobel(dx, axis=1)
        dyy = ndimage.sobel(dy, axis=0)
        dxy = ndimage.sobel(dx, axis=0)

        # 曲率公式: κ = |dxx*dy^2 - 2*dxy*dx*dy + dyy*dx^2| / (dx^2 + dy^2)^(3/2)
        numerator = np.abs(dxx * dy**2 - 2 * dxy * dx * dy + dyy * dx**2)
        denominator = (dx**2 + dy**2)**(3/2)

        # 避免除零
        denominator[denominator < 1e-10] = 1e-10

        curvature = numerator / denominator

        return curvature

    def _apply_interpolated_boundary_conditions(self, curved_boundaries: np.ndarray,
                                              obstacle_mask: np.ndarray) -> None:
        """应用插值边界条件"""
        # 对曲线边界使用二阶精度的插值边界条件

        # 获取边界附近的流体点
        from scipy import ndimage

        fluid_mask = ~obstacle_mask
        boundary_neighbors = ndimage.binary_dilation(curved_boundaries) & fluid_mask

        if np.any(boundary_neighbors):
            # 使用双线性插值计算边界处的分布函数
            for i in range(self.q):
                # 对每个速度方向进行插值
                interpolated_f = self._bilinear_interpolation(
                    self.f[i], curved_boundaries, boundary_neighbors
                )
                self.f[i, curved_boundaries] = interpolated_f[curved_boundaries]

    def _bilinear_interpolation(self, field: np.ndarray, target_points: np.ndarray,
                               source_points: np.ndarray) -> np.ndarray:
        """双线性插值"""
        from scipy import ndimage

        # 使用高斯滤波进行平滑插值
        smoothed_field = ndimage.gaussian_filter(field, sigma=1.0)

        # 在目标点应用插值结果
        result = field.copy()
        result[target_points] = smoothed_field[target_points]

        return result

    def adaptive_boundary_refinement(self, obstacle_mask: np.ndarray,
                                   refinement_level: int = 2) -> np.ndarray:
        """
        自适应边界细化

        参数:
        obstacle_mask: 障碍物掩码
        refinement_level: 细化级别

        返回:
        refined_mask: 细化后的障碍物掩码
        """
        from scipy import ndimage

        # 检测需要细化的区域（高曲率或复杂几何）
        boundaries = self._get_boundary_points(obstacle_mask)
        curvature = self._compute_boundary_curvature(boundaries)

        # 高曲率区域需要细化
        high_curvature_threshold = 0.8
        refinement_regions = boundaries & (curvature > high_curvature_threshold)

        # 对细化区域进行处理
        refined_mask = obstacle_mask.copy()

        if np.any(refinement_regions):
            # 使用形态学操作进行边界平滑
            for _ in range(refinement_level):
                # 轻微的开运算来平滑边界
                refined_mask = ndimage.binary_opening(refined_mask, structure=np.ones((3, 3)))
                # 轻微的闭运算来填补小孔
                refined_mask = ndimage.binary_closing(refined_mask, structure=np.ones((3, 3)))

        return refined_mask

    # ==================== 改进功能：性能优化 ====================

    def parallel_collision_step(self, tau: float, model: str = 'bgk') -> None:
        """
        并行碰撞步骤

        参数:
        tau: 松弛时间
        model: 碰撞模型
        """
        try:
            import multiprocessing as mp
            from concurrent.futures import ThreadPoolExecutor

            # 检查是否有多个CPU核心
            num_cores = mp.cpu_count()

            if num_cores > 1 and self.nx * self.ny > 1000:
                # 对大网格使用并行处理
                self._parallel_collision_implementation(tau, model, num_cores)
            else:
                # 对小网格使用串行处理
                if model == 'bgk':
                    self.collision_bgk(tau)
                elif model == 'mrt':
                    self.collision_mrt(tau)
        except ImportError:
            # 如果并行库不可用，回退到串行处理
            if model == 'bgk':
                self.collision_bgk(tau)
            elif model == 'mrt':
                self.collision_mrt(tau)

    def _parallel_collision_implementation(self, tau: float, model: str, num_cores: int) -> None:
        """并行碰撞实现"""
        # 将网格分割为多个块
        chunk_size = max(self.nx // num_cores, 1)
        chunks = []

        for i in range(0, self.nx, chunk_size):
            end_i = min(i + chunk_size, self.nx)
            chunks.append((i, end_i))

        # 并行处理每个块
        if model == 'bgk':
            self._parallel_bgk_collision(tau, chunks)
        elif model == 'mrt':
            self._parallel_mrt_collision(tau, chunks)

    def _parallel_bgk_collision(self, tau: float) -> None:
        """并行BGK碰撞（使用NumPy向量化和多进程）"""
        try:
            # 方法1：使用NumPy的内置并行化（通过BLAS/LAPACK）
            # 这是最有效的方法，因为NumPy已经优化了大型数组操作
            feq = self.equilibrium_bgk(self.rho, self.u, self.v)

            # 使用向量化操作，NumPy会自动利用多核心
            self.f = self.f - (1/tau) * (self.f - feq)

        except Exception as e:
            print(f"并行BGK碰撞失败，回退到串行计算: {e}")
            # 回退到串行计算
            feq = self.equilibrium_bgk(self.rho, self.u, self.v)
            self.f = self.f - (1/tau) * (self.f - feq)

    def _parallel_bgk_collision_multiprocess(self, tau: float) -> None:
        """使用多进程的并行BGK碰撞（备用方案）"""
        try:
            import multiprocessing as mp
            from functools import partial

            # 创建共享内存数组（如果数据很大）
            if self.nx * self.ny > 50000:  # 大数据集才使用多进程
                with mp.Pool(processes=self.num_cores) as pool:
                    # 分块处理
                    chunk_results = []
                    for start_i, end_i in self.chunks:
                        args = (tau, start_i, end_i,
                               self.rho[start_i:end_i, :].copy(),
                               self.u[start_i:end_i, :].copy(),
                               self.v[start_i:end_i, :].copy(),
                               self.f[:, start_i:end_i, :].copy())
                        result = pool.apply_async(self._bgk_collision_worker, args)
                        chunk_results.append((result, start_i, end_i))

                    # 收集结果
                    for result, start_i, end_i in chunk_results:
                        self.f[:, start_i:end_i, :] = result.get()
            else:
                # 小数据集直接使用向量化
                self._parallel_bgk_collision()

        except Exception as e:
            print(f"多进程BGK碰撞失败，回退到向量化计算: {e}")
            self._parallel_bgk_collision()

    @staticmethod
    def _bgk_collision_worker(tau: float, start_i: int, end_i: int,
                             rho_chunk: np.ndarray, u_chunk: np.ndarray,
                             v_chunk: np.ndarray, f_chunk: np.ndarray) -> np.ndarray:
        """BGK碰撞的工作进程"""
        # 在工作进程中计算平衡分布函数
        q = f_chunk.shape[0]
        ny = f_chunk.shape[2]

        # D2Q9模型的权重和速度
        w = np.array([4/9, 1/9, 1/9, 1/9, 1/9, 1/36, 1/36, 1/36, 1/36])
        c = np.array([[0, 0], [1, 0], [0, 1], [-1, 0], [0, -1],
                     [1, 1], [-1, 1], [-1, -1], [1, -1]])

        # 计算平衡分布函数
        feq = np.zeros_like(f_chunk)
        usqr = u_chunk**2 + v_chunk**2

        for i in range(q):
            cu = c[i, 0] * u_chunk + c[i, 1] * v_chunk
            feq[i] = w[i] * rho_chunk * (1 + 3*cu + 4.5*cu**2 - 1.5*usqr)

        # BGK碰撞
        f_new = f_chunk - (1/tau) * (f_chunk - feq)

        return f_new

    def _parallel_mrt_collision(self, tau: float, chunks: list) -> None:
        """并行MRT碰撞"""
        # 由于MRT涉及矩阵运算，并行化较为复杂
        # 这里提供简化的实现
        for start_i, end_i in chunks:
            # 对每个块应用MRT碰撞
            chunk_f = self.f[:, start_i:end_i, :].copy()
            chunk_rho = self.rho[start_i:end_i, :]
            chunk_u = self.u[start_i:end_i, :]
            chunk_v = self.v[start_i:end_i, :]

            # 计算矩
            m = np.zeros((self.q, end_i - start_i, self.ny))
            for i in range(self.q):
                for j in range(self.q):
                    m[i] += self.M[i, j] * chunk_f[j]

            # 计算平衡态矩
            meq = np.zeros((self.q, end_i - start_i, self.ny))
            usqr = chunk_u**2 + chunk_v**2

            meq[0] = chunk_rho
            meq[1] = -2*chunk_rho + 3*chunk_rho*usqr
            meq[2] = chunk_rho - 3*chunk_rho*usqr
            meq[3] = chunk_rho * chunk_u
            meq[4] = -chunk_rho * chunk_u
            meq[5] = chunk_rho * chunk_v
            meq[6] = -chunk_rho * chunk_v
            meq[7] = chunk_rho * (chunk_u**2 - chunk_v**2)
            meq[8] = chunk_rho * chunk_u * chunk_v

            # 松弛到平衡态
            for i in range(self.q):
                m[i] = m[i] - self.s[i] * (m[i] - meq[i])

            # 转换回分布函数空间
            chunk_f_new = np.zeros((self.q, end_i - start_i, self.ny))
            for i in range(self.q):
                for j in range(self.q):
                    chunk_f_new[i] += self.M_inv[i, j] * m[j]

            self.f[:, start_i:end_i, :] = chunk_f_new

    def memory_optimized_streaming(self) -> None:
        """内存优化的流动步骤"""
        # 使用就地操作减少内存使用
        for i in range(1, self.q):  # 跳过静止方向
            # 使用numpy的roll函数进行就地操作
            self.f[i] = np.roll(self.f[i], self.c[i, 0], axis=0)
            self.f[i] = np.roll(self.f[i], self.c[i, 1], axis=1)

    def check_numerical_stability(self) -> Dict[str, float]:
        """
        检查数值稳定性

        返回:
        stability_metrics: 稳定性指标字典
        """
        metrics = {}

        # 检查密度守恒
        total_mass = np.sum(self.rho)
        metrics['total_mass'] = total_mass
        metrics['mass_conservation_error'] = abs(total_mass - self.nx * self.ny) / (self.nx * self.ny)

        # 检查速度范围
        max_velocity = np.max(np.sqrt(self.u**2 + self.v**2))
        metrics['max_velocity'] = max_velocity
        metrics['velocity_stability'] = max_velocity < 0.3  # 稳定性阈值

        # 检查密度范围
        min_density = np.min(self.rho)
        max_density = np.max(self.rho)
        metrics['min_density'] = min_density
        metrics['max_density'] = max_density
        metrics['density_stability'] = (min_density > 0.1) and (max_density < 3.0)

        # 检查分布函数的物理性
        negative_f = np.sum(self.f < 0)
        metrics['negative_distributions'] = negative_f
        metrics['distribution_stability'] = negative_f == 0

        # 总体稳定性
        metrics['overall_stability'] = (
            metrics['velocity_stability'] and
            metrics['density_stability'] and
            metrics['distribution_stability'] and
            metrics['mass_conservation_error'] < 1e-6
        )

        return metrics
