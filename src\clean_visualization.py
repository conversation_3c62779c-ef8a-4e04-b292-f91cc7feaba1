"""
清洁的可视化模块 - 专注于单图展示
删除无用代码，提供清晰的单图可视化功能
"""

import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from typing import List, Tuple, Optional, Dict
from matplotlib.colors import LinearSegmentedColormap

# 设置matplotlib字体
plt.rcParams['font.family'] = 'Times New Roman'
plt.rcParams['font.size'] = 12


class CleanFluidVisualization:
    """清洁的流体可视化类 - 专注于单图展示"""

    def __init__(self):
        """初始化可视化器"""
        self.figure_count = 0

    def create_single_field_visualizations(self, map_data: Dict, u: np.ndarray, v: np.ndarray,
                                         rho: np.ndarray, speed: np.ndarray, vorticity: np.ndarray,
                                         output_dir: str = "单图可视化",
                                         speed_range: tuple = None, u_range: tuple = None, v_range: tuple = None,
                                         vorticity_range: tuple = None, pressure_range: tuple = None) -> Dict[str, str]:
        """
        创建所有单图可视化

        生成以下单图：
        1. 速度场分布图 (Speed Field)
        2. 速度X分量图 (Velocity X Component)
        3. 速度Y分量图 (Velocity Y Component)
        4. 涡量场分布图 (Vorticity Field)
        5. 压力场分布图 (Pressure Field)
        6. 流线分布图 (Streamline Field)
        7. 三维速度场 (3D Velocity Field)
        8. 三维压力场 (3D Pressure Field)

        参数:
        map_data: 地图数据字典
        u, v: 速度分量
        rho: 密度场
        speed: 速度大小
        vorticity: 涡量场
        output_dir: 输出目录
        speed_range: 速度场颜色范围 (vmin, vmax)，None为自动
        u_range: 速度X分量颜色范围 (vmin, vmax)，None为自动
        v_range: 速度Y分量颜色范围 (vmin, vmax)，None为自动
        vorticity_range: 涡量场颜色范围 (vmin, vmax)，None为自动
        pressure_range: 压力场颜色范围 (vmin, vmax)，None为自动

        返回:
        生成的文件路径字典
        """
        os.makedirs(output_dir, exist_ok=True)
        generated_files = {}

        # 提取地图信息
        original_map = map_data['original_map']
        extension_size = map_data.get('extension_size', 0)
        start_point = map_data['start_point']
        end_point = map_data['end_point']

        # 计算原始地图坐标系中的起点终点
        if extension_size > 0:
            start_original = (start_point[0] - extension_size, start_point[1] - extension_size)
            end_original = (end_point[0] - extension_size, end_point[1] - extension_size)
            h_orig, w_orig = original_map.shape
            start_original = (max(0, min(h_orig-1, start_original[0])),
                            max(0, min(w_orig-1, start_original[1])))
            end_original = (max(0, min(h_orig-1, end_original[0])),
                          max(0, min(w_orig-1, end_original[1])))
        else:
            start_original = start_point
            end_original = end_point

        print("🎨 生成单图可视化...")
        print(f"  地图尺寸: {original_map.shape}")

        # 1. 速度场分布图
        print("  生成速度场分布图...")
        generated_files['速度场分布图'] = self.create_speed_field_plot(
            original_map, speed, start_original, end_original,
            os.path.join(output_dir, "速度场分布图.png"), vmin_vmax=speed_range)

        # 2. 速度X分量图
        print("  生成速度X分量图...")
        generated_files['速度X分量图'] = self.create_velocity_x_plot(
            original_map, u, start_original, end_original,
            os.path.join(output_dir, "速度X分量图.png"), vmin_vmax=u_range)

        # 3. 速度Y分量图
        print("  生成速度Y分量图...")
        generated_files['速度Y分量图'] = self.create_velocity_y_plot(
            original_map, v, start_original, end_original,
            os.path.join(output_dir, "速度Y分量图.png"), vmin_vmax=v_range)

        # 4. 涡量场分布图
        print("  生成涡量场分布图...")
        generated_files['涡量场分布图'] = self.create_vorticity_plot(
            original_map, vorticity, start_original, end_original,
            os.path.join(output_dir, "涡量场分布图.png"), vmin_vmax=vorticity_range)

        # 5. 压力场分布图
        print("  生成压力场分布图...")
        generated_files['压力场分布图'] = self.create_pressure_plot(
            original_map, rho, start_original, end_original,
            os.path.join(output_dir, "压力场分布图.png"), vmin_vmax=pressure_range)

        # 6. 流线分布图
        print("  生成流线分布图...")
        generated_files['流线分布图'] = self.create_streamline_plot(
            original_map, u, v, speed, start_original, end_original,
            os.path.join(output_dir, "流线分布图.png"), vmin_vmax=speed_range)

        # 7. 三维速度场
        print("  生成三维速度场...")
        generated_files['三维速度场'] = self.create_3d_velocity_plot(
            speed, original_map, os.path.join(output_dir, "三维速度场.png"), vmin_vmax=speed_range)

        # 8. 三维压力场
        print("  生成三维压力场...")
        generated_files['三维压力场'] = self.create_3d_pressure_plot(
            rho, original_map, os.path.join(output_dir, "三维压力场.png"), vmin_vmax=pressure_range)

        print(f"  ✅ 共生成 {len(generated_files)} 个单图")
        print(f"  📁 保存位置: {output_dir}")

        return generated_files

    def create_algorithm_comparison_plots(self, traditional_data: Dict, enhanced_data: Dict,
                                        output_dir: str = "算法对比图") -> Dict[str, str]:
        """
        创建LBM算法性能对比图

        生成以下对比图：
        1. 收敛曲线对比图
        2. 速度场对比图
        3. 涡量场对比图
        4. 压力场对比图
        5. 性能指标对比图

        参数:
        traditional_data: 传统LBM数据
        enhanced_data: 增强LBM数据
        output_dir: 输出目录

        返回:
        生成的文件路径字典
        """
        os.makedirs(output_dir, exist_ok=True)
        generated_files = {}

        print("🎨 生成算法对比图...")

        # 1. 收敛曲线对比图
        print("  生成收敛曲线对比图...")
        generated_files['收敛曲线对比图'] = self.create_convergence_comparison_plot(
            traditional_data.get('convergence_history', []),
            enhanced_data.get('convergence_history', []),
            os.path.join(output_dir, "收敛曲线对比图.png"))

        # 2. 速度场对比图
        print("  生成速度场对比图...")
        generated_files['速度场对比图'] = self.create_speed_comparison_plot(
            traditional_data.get('speed'), enhanced_data.get('speed'),
            traditional_data.get('grid_map'),
            os.path.join(output_dir, "速度场对比图.png"))

        # 3. 涡量场对比图
        print("  生成涡量场对比图...")
        generated_files['涡量场对比图'] = self.create_vorticity_comparison_plot(
            traditional_data.get('vorticity'), enhanced_data.get('vorticity'),
            traditional_data.get('grid_map'),
            os.path.join(output_dir, "涡量场对比图.png"))

        # 4. 压力场对比图
        print("  生成压力场对比图...")
        generated_files['压力场对比图'] = self.create_pressure_comparison_plot(
            traditional_data.get('rho'), enhanced_data.get('rho'),
            traditional_data.get('grid_map'),
            os.path.join(output_dir, "压力场对比图.png"))

        # 5. 性能指标对比图
        print("  生成性能指标对比图...")
        generated_files['性能指标对比图'] = self.create_performance_comparison_plot(
            traditional_data.get('metrics', {}), enhanced_data.get('metrics', {}),
            os.path.join(output_dir, "性能指标对比图.png"))

        print(f"  ✅ 共生成 {len(generated_files)} 个对比图")
        print(f"  📁 保存位置: {output_dir}")

        return generated_files

    def _clean_data(self, data: np.ndarray) -> np.ndarray:
        """清理数据，移除NaN和Inf值"""
        return np.nan_to_num(data, nan=0.0, posinf=0.0, neginf=0.0)

    def _add_start_end_points(self, ax, start_point: Tuple[int, int], end_point: Tuple[int, int]):
        """添加起点和终点标记"""
        if start_point and end_point:
            # 起点 (绿色圆圈)
            ax.scatter(start_point[1], start_point[0], color='lime', s=200, marker='o',
                      edgecolors='black', linewidth=3, label='Start', zorder=10)

            # 终点 (红色方块)
            ax.scatter(end_point[1], end_point[0], color='red', s=200, marker='s',
                      edgecolors='black', linewidth=3, label='End', zorder=10)

    def _add_obstacles(self, ax, grid_map: np.ndarray):
        """添加障碍物边界"""
        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))
        ax.contour(x, y, grid_map, levels=[0.5], colors='black', linewidths=2, alpha=0.8)

    # ==================== 单图可视化方法 ====================

    def create_speed_field_plot(self, grid_map: np.ndarray, speed: np.ndarray,
                               start_point: Tuple[int, int], end_point: Tuple[int, int],
                               save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建速度场分布图

        参数:
        grid_map: 网格地图
        speed: 速度场
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        speed_clean = self._clean_data(speed)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            vmin, vmax = np.min(speed_clean), np.max(speed_clean)

        # 速度场背景
        im = ax.imshow(speed_clean, cmap='viridis', origin='upper', alpha=0.9, vmin=0, vmax=0.001)

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Velocity Field Distribution', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_speed = np.max(speed_clean)
        avg_speed = np.mean(speed_clean[grid_map == 0])
        info_text = f'Max Speed: {max_speed:.4f} m/s\nAvg Speed: {avg_speed:.4f} m/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_velocity_x_plot(self, grid_map: np.ndarray, u: np.ndarray,
                              start_point: Tuple[int, int], end_point: Tuple[int, int],
                              save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建速度X分量图

        参数:
        grid_map: 网格地图
        u: 速度X分量
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算对称范围
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        u_clean = self._clean_data(u)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            # 计算对称的颜色范围
            u_max = np.percentile(np.abs(u_clean), 95)
            vmin, vmax = -u_max, u_max

        # 速度X分量
        im = ax.imshow(u_clean, cmap='RdBu_r', origin='upper', alpha=0.9, vmin=vmin, vmax=vmax)

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Velocity X Component', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('X Velocity (m/s)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_u = np.max(u_clean)
        min_u = np.min(u_clean)
        avg_u = np.mean(u_clean[grid_map == 0])
        info_text = f'Max: {max_u:.4f} m/s\nMin: {min_u:.4f} m/s\nAvg: {avg_u:.4f} m/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_velocity_y_plot(self, grid_map: np.ndarray, v: np.ndarray,
                              start_point: Tuple[int, int], end_point: Tuple[int, int],
                              save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建速度Y分量图

        参数:
        grid_map: 网格地图
        v: 速度Y分量
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算对称范围
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        v_clean = self._clean_data(v)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            # 计算对称的颜色范围
            v_max = np.percentile(np.abs(v_clean), 95)
            vmin, vmax = -v_max, v_max

        # 速度Y分量
        im = ax.imshow(v_clean, cmap='RdBu_r', origin='upper', alpha=0.9, vmin=vmin, vmax=vmax)

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Velocity Y Component', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Y Velocity (m/s)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_v = np.max(v_clean)
        min_v = np.min(v_clean)
        avg_v = np.mean(v_clean[grid_map == 0])
        info_text = f'Max: {max_v:.4f} m/s\nMin: {min_v:.4f} m/s\nAvg: {avg_v:.4f} m/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_vorticity_plot(self, grid_map: np.ndarray, vorticity: np.ndarray,
                             start_point: Tuple[int, int], end_point: Tuple[int, int],
                             save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建涡量场分布图

        参数:
        grid_map: 网格地图
        vorticity: 涡量场
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算对称范围
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        vorticity_clean = self._clean_data(vorticity)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            # 计算对称的颜色范围
            vort_max = np.percentile(np.abs(vorticity_clean), 95)
            vmin, vmax = -vort_max, vort_max

        # 涡量场
        im = ax.imshow(vorticity_clean, cmap='seismic', origin='upper', alpha=0.9, vmin=vmin, vmax=vmax)

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Vorticity Field Distribution', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Vorticity (1/s)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_vort = np.max(vorticity_clean)
        min_vort = np.min(vorticity_clean)
        avg_vort = np.mean(np.abs(vorticity_clean[grid_map == 0]))
        info_text = f'Max: {max_vort:.6f} 1/s\nMin: {min_vort:.6f} 1/s\nAvg |ω|: {avg_vort:.6f} 1/s'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_pressure_plot(self, grid_map: np.ndarray, rho: np.ndarray,
                            start_point: Tuple[int, int], end_point: Tuple[int, int],
                            save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建压力场分布图

        参数:
        grid_map: 网格地图
        rho: 密度场
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        rho_clean = self._clean_data(rho)

        # 计算压力 (P = ρ * cs², cs² = 1/3 in LBM)
        pressure = rho_clean * (1.0 / 3.0)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            vmin, vmax = np.min(pressure), np.max(pressure)

        # 压力场
        im = ax.imshow(pressure, cmap='RdYlBu_r', origin='upper', alpha=0.9, vmin=vmin, vmax=vmax)

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Pressure Field Distribution', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Pressure (Pa)', fontsize=12, fontweight='bold')

        # 添加统计信息
        max_pressure = np.max(pressure)
        min_pressure = np.min(pressure)
        avg_pressure = np.mean(pressure[grid_map == 0])
        info_text = f'Max: {max_pressure:.6f} Pa\nMin: {min_pressure:.6f} Pa\nAvg: {avg_pressure:.6f} Pa'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_streamline_plot(self, grid_map: np.ndarray, u: np.ndarray, v: np.ndarray,
                              speed: np.ndarray, start_point: Tuple[int, int], end_point: Tuple[int, int],
                              save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建流线分布图

        参数:
        grid_map: 网格地图
        u, v: 速度分量
        speed: 速度大小
        start_point: 起点
        end_point: 终点
        save_path: 保存路径
        vmin_vmax: 背景速度场颜色范围 (vmin, vmax)，None为自动计算
        """
        fig, ax = plt.subplots(figsize=(12, 10), dpi=500)

        # 清理数据
        u_clean = self._clean_data(u)
        v_clean = self._clean_data(v)
        speed_clean = self._clean_data(speed)

        h, w = grid_map.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 创建掩码，避开障碍物
        mask = (grid_map == 0) & (speed_clean > 1e-6)
        u_masked = np.where(mask, u_clean, 0)
        v_masked = np.where(mask, v_clean, 0)

        # 确定背景速度场颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            vmin, vmax = np.min(speed_clean), np.max(speed_clean)

        # 背景速度场
        im = ax.imshow(speed_clean, cmap='viridis', origin='upper', alpha=0.6, vmin=vmin, vmax=vmax)

        # 流线图
        try:
            ax.streamplot(x, y, u_masked, v_masked, density=2.0,
                         color='white', linewidth=1.5, arrowsize=2)
        except:
            # 如果streamplot失败，使用简化版本
            try:
                ax.streamplot(x, y, u_masked, v_masked, density=1.0,
                             color='white', linewidth=1)
            except:
                pass  # 如果还是失败，跳过流线

        # 添加障碍物边界
        self._add_obstacles(ax, grid_map)

        # 添加起点终点
        self._add_start_end_points(ax, start_point, end_point)

        # 设置标题和标签
        ax.set_title('Streamline Field Distribution', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=14)
        ax.set_ylabel('Y Position', fontsize=14)

        # 颜色条
        cbar = plt.colorbar(im, ax=ax, shrink=0.8)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')

        # 添加统计信息
        fluid_points = np.sum(mask)
        total_points = mask.size
        coverage = fluid_points / total_points * 100
        info_text = f'Fluid Coverage: {coverage:.1f}%\nStreamlines: Flow paths'
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               verticalalignment='top', fontsize=11,
               bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_3d_velocity_plot(self, speed: np.ndarray, grid_map: np.ndarray, save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建三维速度场

        参数:
        speed: 速度场
        grid_map: 网格地图
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算
        """
        fig = plt.figure(figsize=(14, 10), dpi=500)
        ax = fig.add_subplot(111, projection='3d')

        # 清理数据
        speed_clean = self._clean_data(speed)

        h, w = speed_clean.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 只显示流体区域
        mask = (grid_map == 0)
        speed_masked = np.where(mask, speed_clean, np.nan)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            vmin, vmax = np.nanmin(speed_masked), np.nanmax(speed_masked)

        # 3D表面图
        surf = ax.plot_surface(x, y, speed_masked, cmap='viridis', alpha=0.8,
                              linewidth=0, antialiased=True, vmin=vmin, vmax=vmax)

        # 设置标题和标签
        ax.set_title('3D Velocity Field', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=12)
        ax.set_ylabel('Y Position', fontsize=12)
        ax.set_zlabel('Velocity Magnitude (m/s)', fontsize=12)

        # 颜色条
        cbar = plt.colorbar(surf, ax=ax, shrink=0.6)
        cbar.set_label('Velocity Magnitude (m/s)', fontsize=12, fontweight='bold')

        # 设置视角
        ax.view_init(elev=30, azim=45)

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_3d_pressure_plot(self, rho: np.ndarray, grid_map: np.ndarray, save_path: str, vmin_vmax: tuple = None) -> str:
        """
        创建三维压力场

        参数:
        rho: 密度场
        grid_map: 网格地图
        save_path: 保存路径
        vmin_vmax: 颜色范围 (vmin, vmax)，None为自动计算
        """
        fig = plt.figure(figsize=(14, 10), dpi=500)
        ax = fig.add_subplot(111, projection='3d')

        # 清理数据
        rho_clean = self._clean_data(rho)

        # 计算压力
        pressure = rho_clean * (1.0 / 3.0)

        h, w = pressure.shape
        x, y = np.meshgrid(np.arange(w), np.arange(h))

        # 只显示流体区域
        mask = (grid_map == 0)
        pressure_masked = np.where(mask, pressure, np.nan)

        # 确定颜色范围
        if vmin_vmax is not None:
            vmin, vmax = vmin_vmax
        else:
            vmin, vmax = np.nanmin(pressure_masked), np.nanmax(pressure_masked)

        # 3D表面图
        surf = ax.plot_surface(x, y, pressure_masked, cmap='RdYlBu_r', alpha=0.8,
                              linewidth=0, antialiased=True, vmin=vmin, vmax=vmax)

        # 设置标题和标签
        ax.set_title('3D Pressure Field', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('X Position', fontsize=12)
        ax.set_ylabel('Y Position', fontsize=12)
        ax.set_zlabel('Pressure (Pa)', fontsize=12)

        # 颜色条
        cbar = plt.colorbar(surf, ax=ax, shrink=0.6)
        cbar.set_label('Pressure (Pa)', fontsize=12, fontweight='bold')

        # 设置视角
        ax.view_init(elev=30, azim=45)

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    # ==================== 算法对比可视化方法 ====================

    def create_convergence_comparison_plot(self, traditional_history: List[float],
                                         enhanced_history: List[float], save_path: str) -> str:
        """创建收敛曲线对比图"""
        fig, ax = plt.subplots(figsize=(12, 8), dpi=500)

        # 绘制收敛曲线
        if traditional_history:
            iterations_trad = range(len(traditional_history))
            ax.semilogy(iterations_trad, traditional_history, 'b-', linewidth=2,
                       label='Traditional LBM', marker='o', markersize=4, alpha=0.8)

        if enhanced_history:
            iterations_enh = range(len(enhanced_history))
            ax.semilogy(iterations_enh, enhanced_history, 'r-', linewidth=2,
                       label='Enhanced LBM', marker='s', markersize=4, alpha=0.8)

        # 设置标题和标签
        ax.set_title('Convergence Comparison', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('Iteration', fontsize=14)
        ax.set_ylabel('Residual (log scale)', fontsize=14)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=12)

        # 添加统计信息
        if traditional_history and enhanced_history:
            final_trad = traditional_history[-1] if traditional_history else 0
            final_enh = enhanced_history[-1] if enhanced_history else 0
            improvement = (final_trad - final_enh) / final_trad * 100 if final_trad > 0 else 0

            info_text = f'Final Residuals:\nTraditional: {final_trad:.2e}\nEnhanced: {final_enh:.2e}\nImprovement: {improvement:.1f}%'
            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                   verticalalignment='top', fontsize=11,
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_speed_comparison_plot(self, traditional_speed: np.ndarray, enhanced_speed: np.ndarray,
                                   grid_map: np.ndarray, save_path: str) -> str:
        """创建速度场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 清理数据
        trad_clean = self._clean_data(traditional_speed) if traditional_speed is not None else np.zeros_like(grid_map)
        enh_clean = self._clean_data(enhanced_speed) if enhanced_speed is not None else np.zeros_like(grid_map)

        # 统一颜色范围
        vmax = max(np.max(trad_clean), np.max(enh_clean))

        # 传统LBM
        im1 = ax1.imshow(trad_clean, cmap='viridis', origin='upper', vmin=0, vmax=vmax)
        self._add_obstacles(ax1, grid_map)
        ax1.set_title('Traditional LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im1, ax=ax1, shrink=0.8)

        # 增强LBM
        im2 = ax2.imshow(enh_clean, cmap='viridis', origin='upper', vmin=0, vmax=vmax)
        self._add_obstacles(ax2, grid_map)
        ax2.set_title('Enhanced LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im2, ax=ax2, shrink=0.8)

        # 差异图
        diff = enh_clean - trad_clean
        diff_max = np.max(np.abs(diff))
        im3 = ax3.imshow(diff, cmap='RdBu_r', origin='upper', vmin=-diff_max, vmax=diff_max)
        self._add_obstacles(ax3, grid_map)
        ax3.set_title('Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        plt.colorbar(im3, ax=ax3, shrink=0.8)

        # 计算改进百分比
        if np.max(trad_clean) > 0:
            improvement = (np.max(enh_clean) - np.max(trad_clean)) / np.max(trad_clean) * 100
            fig.suptitle(f'Velocity Field Comparison (Max Speed Improvement: {improvement:.1f}%)',
                        fontsize=16, fontweight='bold')
        else:
            fig.suptitle('Velocity Field Comparison', fontsize=16, fontweight='bold')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_vorticity_comparison_plot(self, traditional_vorticity: np.ndarray, enhanced_vorticity: np.ndarray,
                                       grid_map: np.ndarray, save_path: str) -> str:
        """创建涡量场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 清理数据
        trad_clean = self._clean_data(traditional_vorticity) if traditional_vorticity is not None else np.zeros_like(grid_map)
        enh_clean = self._clean_data(enhanced_vorticity) if enhanced_vorticity is not None else np.zeros_like(grid_map)

        # 统一颜色范围
        vort_max = max(np.percentile(np.abs(trad_clean), 95), np.percentile(np.abs(enh_clean), 95))
        vmin, vmax = -vort_max, vort_max

        # 传统LBM
        im1 = ax1.imshow(trad_clean, cmap='seismic', origin='upper', vmin=vmin, vmax=vmax)
        self._add_obstacles(ax1, grid_map)
        ax1.set_title('Traditional LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im1, ax=ax1, shrink=0.8)

        # 增强LBM
        im2 = ax2.imshow(enh_clean, cmap='seismic', origin='upper', vmin=vmin, vmax=vmax)
        self._add_obstacles(ax2, grid_map)
        ax2.set_title('Enhanced LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im2, ax=ax2, shrink=0.8)

        # 差异图
        diff = enh_clean - trad_clean
        diff_max = np.max(np.abs(diff))
        im3 = ax3.imshow(diff, cmap='RdBu_r', origin='upper', vmin=-diff_max, vmax=diff_max)
        self._add_obstacles(ax3, grid_map)
        ax3.set_title('Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        plt.colorbar(im3, ax=ax3, shrink=0.8)

        fig.suptitle('Vorticity Field Comparison', fontsize=16, fontweight='bold')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_pressure_comparison_plot(self, traditional_rho: np.ndarray, enhanced_rho: np.ndarray,
                                      grid_map: np.ndarray, save_path: str) -> str:
        """创建压力场对比图"""
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(18, 6), dpi=500)

        # 清理数据并计算压力
        trad_clean = self._clean_data(traditional_rho) * (1.0/3.0) if traditional_rho is not None else np.zeros_like(grid_map)
        enh_clean = self._clean_data(enhanced_rho) * (1.0/3.0) if enhanced_rho is not None else np.zeros_like(grid_map)

        # 统一颜色范围
        vmin = min(np.min(trad_clean), np.min(enh_clean))
        vmax = max(np.max(trad_clean), np.max(enh_clean))

        # 传统LBM
        im1 = ax1.imshow(trad_clean, cmap='RdYlBu_r', origin='upper', vmin=vmin, vmax=vmax)
        self._add_obstacles(ax1, grid_map)
        ax1.set_title('Traditional LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im1, ax=ax1, shrink=0.8)

        # 增强LBM
        im2 = ax2.imshow(enh_clean, cmap='RdYlBu_r', origin='upper', vmin=vmin, vmax=vmax)
        self._add_obstacles(ax2, grid_map)
        ax2.set_title('Enhanced LBM', fontsize=14, fontweight='bold')
        plt.colorbar(im2, ax=ax2, shrink=0.8)

        # 差异图
        diff = enh_clean - trad_clean
        diff_max = np.max(np.abs(diff))
        im3 = ax3.imshow(diff, cmap='RdBu_r', origin='upper', vmin=-diff_max, vmax=diff_max)
        self._add_obstacles(ax3, grid_map)
        ax3.set_title('Difference (Enhanced - Traditional)', fontsize=14, fontweight='bold')
        plt.colorbar(im3, ax=ax3, shrink=0.8)

        fig.suptitle('Pressure Field Comparison', fontsize=16, fontweight='bold')

        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path

    def create_performance_comparison_plot(self, traditional_metrics: Dict, enhanced_metrics: Dict,
                                         save_path: str) -> str:
        """创建性能指标对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12), dpi=500)

        # 提取性能指标
        metrics_names = ['simulation_time', 'final_max_velocity', 'velocity_smoothness', 'avg_oscillation']
        metrics_labels = ['Simulation Time (s)', 'Max Velocity (m/s)', 'Velocity Smoothness', 'Avg Oscillation']

        traditional_values = [traditional_metrics.get(m, 0) for m in metrics_names]
        enhanced_values = [enhanced_metrics.get(m, 0) for m in metrics_names]

        axes = [ax1, ax2, ax3, ax4]

        for ax, name, label, trad_val, enh_val in zip(axes, metrics_names, metrics_labels, traditional_values, enhanced_values):
            # 柱状图
            x = ['Traditional LBM', 'Enhanced LBM']
            y = [trad_val, enh_val]
            colors = ['skyblue', 'lightcoral']

            bars = ax.bar(x, y, color=colors, alpha=0.8, edgecolor='black', linewidth=1)

            # 添加数值标签
            for bar, val in zip(bars, y):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                       f'{val:.6f}' if val < 1 else f'{val:.3f}',
                       ha='center', va='bottom', fontsize=10, fontweight='bold')

            ax.set_title(label, fontsize=12, fontweight='bold')
            ax.set_ylabel('Value', fontsize=10)
            ax.grid(True, alpha=0.3)

            # 计算改进百分比
            if trad_val > 0:
                if name in ['simulation_time', 'avg_oscillation']:  # 越小越好
                    improvement = (trad_val - enh_val) / trad_val * 100
                else:  # 越大越好
                    improvement = (enh_val - trad_val) / trad_val * 100

                ax.text(0.5, 0.95, f'Improvement: {improvement:.1f}%',
                       transform=ax.transAxes, ha='center', va='top',
                       bbox=dict(boxstyle='round', facecolor='yellow', alpha=0.7),
                       fontsize=10, fontweight='bold')

        fig.suptitle('Performance Metrics Comparison', fontsize=16, fontweight='bold')
        plt.tight_layout()
        fig.savefig(save_path, dpi=500, bbox_inches='tight')
        plt.close()
        return save_path